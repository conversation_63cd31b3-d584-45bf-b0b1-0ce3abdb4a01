apiVersion: kibana.k8s.elastic.co/v1
kind: Kibana
metadata:
  name: dre-telemetry-kibana
spec:
  version: 8.11.3
  count: 1
  elasticsearchRef:
    name: dre-telemetry-eck
    namespace: internal
  podTemplate:
    spec:
      containers:
        - name: kibana
          resources:
            requests:
              cpu: 500m
              memory: 1Gi
            limits:
              cpu: 2
              memory: 2.5Gi
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /
              port: 5601
              scheme: HTTPS
