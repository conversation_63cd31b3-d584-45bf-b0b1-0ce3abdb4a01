apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: cluster-setup
  # You'll usually want to add your resources to the argocd namespace.
  namespace: argocd
  # Add a this finalizer ONLY if you want these to cascade delete.
  # finalizers:
  #   - resources-finalizer.argocd.argoproj.io
spec:
  # The project the application belongs to.
  project: cluster-setup

  # Source of the application manifests
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
    targetRevision: master
    path: ./apps/cluster

  # Destination cluster and namespace to deploy the application
  destination:
    server: https://kubernetes.default.svc
    namespace: argocd

  # Because the kustomize base pulls in an argocd-secret and External-Secret isn't generating the same labels, we want argo to ignore them for now
  ignoreDifferences:
    - kind: Secret
      name: argocd-secret
      namespace: argocd
      jsonPointers:
        - /metadata/labels

  # Sync policy
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: cluster-administration
  # You'll usually want to add your resources to the argocd namespace.
  namespace: argocd
  # Add a this finalizer ONLY if you want these to cascade delete.
  # finalizers:
  #   - resources-finalizer.argocd.argoproj.io
spec:
  # The project the application belongs to.
  project: cluster-administration

  # Source of the application manifests
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
    targetRevision: master
    path: ./apps

  # Destination cluster and namespace to deploy the application
  destination:
    server: https://kubernetes.default.svc
    namespace: argocd

  # Sync policy
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: kingston
  # You'll usually want to add your resources to the argocd namespace.
  namespace: argocd
  # Add a this finalizer ONLY if you want these to cascade delete.
  # finalizers:
  #   - resources-finalizer.argocd.argoproj.io
spec:
  # The project the application belongs to.
  project: kingston

  # Source of the application manifests
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
    targetRevision: master
    path: ./apps/dre-cobra/kingston

  # Destination cluster and namespace to deploy the application
  destination:
    server: https://kubernetes.default.svc
    namespace: argocd

  # Sync policy
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: internal
  # You'll usually want to add your resources to the argocd namespace.
  namespace: argocd
  # Add a this finalizer ONLY if you want these to cascade delete.
  # finalizers:
  #   - resources-finalizer.argocd.argoproj.io
spec:
  # The project the application belongs to.
  project: internal

  # Source of the application manifests
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
    targetRevision: master
    path: ./apps/dre-cobra/internal

  # Destination cluster and namespace to deploy the application
  destination:
    server: https://kubernetes.default.svc
    namespace: argocd

  # Sync policy
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: observability
  # You'll usually want to add your resources to the argocd namespace.
  namespace: argocd
  # Add a this finalizer ONLY if you want these to cascade delete.
  # finalizers:
  #   - resources-finalizer.argocd.argoproj.io
spec:
  # The project the application belongs to.
  project: observability

  # Source of the application manifests
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
    targetRevision: master
    path: ./apps/dre-cobra/observability

  # Destination cluster and namespace to deploy the application
  destination:
    server: https://kubernetes.default.svc
    namespace: argocd

  # Sync policy
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: misc
  # You'll usually want to add your resources to the argocd namespace.
  namespace: argocd
  # Add a this finalizer ONLY if you want these to cascade delete.
  # finalizers:
  #   - resources-finalizer.argocd.argoproj.io
spec:
  # The project the application belongs to.
  project: misc

  # Source of the application manifests
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
    targetRevision: master
    path: ./apps/dre-cobra/misc

  # Destination cluster and namespace to deploy the application
  destination:
    server: https://kubernetes.default.svc
    namespace: argocd

  # Sync policy
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: fb1
  # You'll usually want to add your resources to the argocd namespace.
  namespace: argocd
  # Add a this finalizer ONLY if you want these to cascade delete.
  # finalizers:
  #   - resources-finalizer.argocd.argoproj.io
spec:
  # The project the application belongs to.
  project: fb1

  # Source of the application manifests
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
    targetRevision: master
    path: ./apps/dre-cobra/fb1

  # Destination cluster and namespace to deploy the application
  destination:
    server: https://kubernetes.default.svc
    namespace: argocd

  # Sync policy
  syncPolicy:
    automated:
      prune: true
      selfHeal: true

---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: dice
  # You'll usually want to add your resources to the argocd namespace.
  namespace: argocd
  # Add a this finalizer ONLY if you want these to cascade delete.
  # finalizers:
  #   - resources-finalizer.argocd.argoproj.io
spec:
  # The project the application belongs to.
  project: dice

  # Source of the application manifests
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
    targetRevision: master
    path: ./apps/dre-cobra/dice

  # Destination cluster and namespace to deploy the application
  destination:
    server: https://kubernetes.default.svc
    namespace: argocd

  # Sync policy
  syncPolicy:
    automated:
      prune: true
      selfHeal: true

---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: granite
  # You'll usually want to add your resources to the argocd namespace.
  namespace: argocd
  # Add a this finalizer ONLY if you want these to cascade delete.
  # finalizers:
  #   - resources-finalizer.argocd.argoproj.io
spec:
  # The project the application belongs to.
  project: granite

  # Source of the application manifests
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
    targetRevision: master
    path: ./apps/dre-cobra/granite

  # Destination cluster and namespace to deploy the application
  destination:
    server: https://kubernetes.default.svc
    namespace: argocd

  # Sync policy
  syncPolicy:
    automated:
      prune: true
      selfHeal: true

---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: bct
  # You'll usually want to add your resources to the argocd namespace.
  namespace: argocd
  # Add a this finalizer ONLY if you want these to cascade delete.
  # finalizers:
  #   - resources-finalizer.argocd.argoproj.io
spec:
  # The project the application belongs to.
  project: bct

  # Source of the application manifests
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
    targetRevision: master
    path: ./apps/dre-cobra/bct

  # Destination cluster and namespace to deploy the application
  destination:
    server: https://kubernetes.default.svc
    namespace: argocd

  # Sync policy
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
