apiVersion: v1
kind: ConfigMap
metadata:
  name: ldap-config
  namespace: observability
data:
  ldap.toml: |
    # Set to true to log user information returned from LDAP
    verbose_logging = false

    [[servers]]
    # Ldap server host (specify multiple hosts space separated)
    host = "lhr-ldap-vip.ad.ea.com"
    # Default port is 389 or 636 if use_ssl = true
    port = 3269
    # Set to true if ldap server supports TLS
    use_ssl = true
    # Set to true if connect ldap server with STARTTLS pattern
    start_tls = false
    # set to true if you want to skip ssl cert validation
    ssl_skip_verify = false
    # set to the path to your root CA certificate or leave unset to use system defaults
    # root_ca_cert = "/path/to/certificate.crt"
    allow_sign_up = true

    # Search user bind dn
    bind_dn = "$__file{/etc/grafana-secrets/ldap-secrets/dex.bind_dn}"
    # Search user bind password
    # If the password contains # or ; you have to wrap it with triple quotes. Ex """#password;"""
    bind_password = """$__file{/etc/grafana-secrets/ldap-secrets/dex.bind_password}"""

    # User search filter, for example "(cn=%s)" or "(sAMAccountName=%s)" or "(uid=%s)"
    search_filter = "(UserPrincipalName=%s)"

    # An array of base dns to search through
    search_base_dns = ["DC=ad,DC=ea,DC=com"]

    # In POSIX LDAP schemas, without memberOf attribute a secondary query must be made for groups.
    # This is done by enabling group_search_filter below. You must also set member_of= "cn"
    # in [servers.attributes] below.

    group_search_filter = "(member:1.2.840.113556.1.4.1941:=%s)"
    group_search_filter_user_attribute = "distinguishedName"
    group_search_base_dns = ["OU=Groups,DC=ad,DC=ea,DC=com"]

    # Specify names of the ldap attributes your ldap uses
    [servers.attributes]
    name = "givenName"
    surname = "sn"
    username = "userPrincipalName"
    member_of = "distinguishedName"
    # "memberOf"
    email =  "mail"

    ######## Group Mappings #######
    # Map ldap groups to grafana org roles
    [[servers.group_mappings]]
    group_dn = "CN=DRE.Cobra.Engineers,OU=groups.ea.com,OU=Groups,DC=ad,DC=ea,DC=com"
    org_role = "Admin"

    [[servers.group_mappings]]
    # If you want to match all (or no ldap groups) then you can use wildcard
    group_dn = "*"
    org_role = "Viewer"
