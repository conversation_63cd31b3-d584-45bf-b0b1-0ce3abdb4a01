apiVersion: integreatly.org/v1alpha1
kind: GrafanaDashboardList
items:
  - apiVersion: integreatly.org/v1alpha1
    kind: GrafanaDashboard
    metadata:
      name: grafana-dashboard-cobra-loki-chunks
      labels:
        app: grafana
    spec:
      json:
        ""
      configMapRef:
        name: loki-dashboards-1
        key: loki-chunks.json
  - apiVersion: integreatly.org/v1alpha1
    kind: GrafanaDashboard
    metadata:
      name: grafana-dashboard-cobra-loki-logs
      labels:
        app: grafana
    spec:
      json:
        ""
      configMapRef:
        name: loki-dashboards-1
        key: loki-logs.json
  - apiVersion: integreatly.org/v1alpha1
    kind: GrafanaDashboard
    metadata:
      name: grafana-dashboard-cobra-loki-mixin-recording-rules
      labels:
        app: grafana
    spec:
      json:
        ""
      configMapRef:
        name: loki-dashboards-1
        key: loki-mixin-recording-rules.json
  - apiVersion: integreatly.org/v1alpha1
    kind: GrafanaDashboard
    metadata:
      name: grafana-dashboard-cobra-loki-operational
      labels:
        app: grafana
    spec:
      json:
        ""
      configMapRef:
        name: loki-dashboards-1
        key: loki-operational.json
  - apiVersion: integreatly.org/v1alpha1
    kind: GrafanaDashboard
    metadata:
      name: grafana-dashboard-cobra-loki-reads-resources
      labels:
        app: grafana
    spec:
      json:
        ""
      configMapRef:
        name: loki-dashboards-2
        key: loki-reads-resources.json
  - apiVersion: integreatly.org/v1alpha1
    kind: GrafanaDashboard
    metadata:
      name: grafana-dashboard-cobra-loki-reads
      labels:
        app: grafana
    spec:
      json:
        ""
      configMapRef:
        name: loki-dashboards-2
        key: loki-reads.json
  - apiVersion: integreatly.org/v1alpha1
    kind: GrafanaDashboard
    metadata:
      name: grafana-dashboard-cobra-loki-retention
      labels:
        app: grafana
    spec:
      json:
        ""
      configMapRef:
        name: loki-dashboards-2
        key: loki-retention.json
  - apiVersion: integreatly.org/v1alpha1
    kind: GrafanaDashboard
    metadata:
      name: grafana-dashboard-cobra-loki-writes-resources
      labels:
        app: grafana
    spec:
      json:
        ""
      configMapRef:
        name: loki-dashboards-2
        key: loki-writes-resources.json
  - apiVersion: integreatly.org/v1alpha1
    kind: GrafanaDashboard
    metadata:
      name: grafana-dashboard-cobra-loki-writes
      labels:
        app: grafana
    spec:
      json:
        ""
      configMapRef:
        name: loki-dashboards-2
        key: loki-writes.json
