# internal cluster url: http://dice-metrics-eck-es-http.dice.svc.cluster.local:9200
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dice-metrics-eck-ingress
  annotations:
    traefik.ingress.kubernetes.io/router.entrypoints: websecure
    traefik.ingress.kubernetes.io/router.middlewares: traefik-https-redirect@kubernetescrd
spec:
  tls:
    - hosts:
        - dice-metrics-eck.cobra.dre.ea.com
  rules:
    - host: dice-metrics-eck.cobra.dre.ea.com
      http:
        paths:
          - backend:
              service:
                name: dice-metrics-eck-es-http
                port:
                  number: 9200
            path: /
            pathType: Prefix
