apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: prometheus
  namespace: observability
  annotations:
    # Use SSL
    traefik.ingress.kubernetes.io/router.entrypoints: web,websecure
    traefik.ingress.kubernetes.io/router.middlewares: "traefik-https-redirect@kubernetescrd"
spec:
  rules:
  - host: prometheus.cobra.dre.ea.com
    http:
      paths:
      - backend:
          service:
            name: prometheus-k8s
            port:
              name: web
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - prometheus.cobra.dre.ea.com