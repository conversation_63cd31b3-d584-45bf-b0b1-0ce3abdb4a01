apiVersion: batch/v1
kind: CronJob
metadata:
  name: loki-log-parser-cron
spec:
  schedule: "0 * * * *"
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: loki-log-parser
        spec:
          containers:
          - name: loki-log-parser
            image: dre-docker-federated.artifacts.ea.com/cobra/loki-log-parser:latest
            imagePullPolicy: "Always"
            resources:
              requests:
                memory: "64Mi"
                cpu: "100m"
              limits:
                memory: "512Mi"
                cpu: "500m"
            envFrom:
              - secretRef:
                  name: loki-log-parser-secrets
          restartPolicy: OnFailure
