package scripts.schedulers.frosty

import com.ea.lib.LibCommonCps
import com.ea.lib.LibCommonNonCps
import com.ea.lib.LibJenkins
import com.ea.project.GetBranchFile

def project = ProjectClass(env.project_name)

/**
 * patchfrosty_start.groovy
 */
pipeline {
    agent { label '(scheduler && master) || scheduler || executor_agent' }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger patchfrosty jobs') {
            steps {
                script {
                    def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)

                    def last_good_code = LibJenkins.getLastStableCodeChangelist(env.frosty_reference_job)
                    def last_good_data = LibJenkins.getLastStableDataChangelist(env.frosty_reference_job)

                    def code_changelist = params.code_changelist ?: last_good_code
                    def data_changelist = params.data_changelist ?: last_good_data
                    def clean_data = params.clean_data
                    def args = [
                        string(name: 'code_changelist', value: code_changelist),
                        string(name: 'data_changelist', value: data_changelist),
                        string(name: 'clean_data', value: clean_data),
                    ]

                    def inject_map = [
                        'code_changelist': code_changelist,
                        'data_changelist': data_changelist,
                    ]

                    def combine_code_changelist = ''
                    def combine_data_changelist = ''
                    if (branchfile.standard_jobs_settings.combine_bundles?.is_target_branch) {
                        combine_code_changelist = LibJenkins.getLastStableCodeChangelist(env.combine_reference_job)
                        combine_data_changelist = LibJenkins.getLastStableDataChangelist(env.combine_reference_job)
                        inject_map += [
                            'combine_code_changelist': combine_code_changelist,
                            'combine_data_changelist': combine_data_changelist,
                        ]
                    }

                    EnvInject(currentBuild, inject_map)

                    // Set display name including SP changelists when available for combined jobs
                    def display_changelists = [data_changelist, code_changelist]
                    if (branchfile.standard_jobs_settings.combine_bundles?.is_target_branch) {
                        display_changelists += [combine_data_changelist, combine_code_changelist]
                    }
                    SetDisplayName(currentBuild, display_changelists)

                    def jobs = [:]

                    def frosty_matrix = branchfile.frosty_for_patch_matrix
                    def patchfrosty_matrix = branchfile.patchfrosty_matrix

                    // Check if separate combined bundles jobs should be created
                    def use_separate_combined_job = branchfile.standard_jobs_settings.combine_bundles?.use_separate_combined_job ?: false
                    def combined_job_platforms = branchfile.standard_jobs_settings.combine_bundles?.combined_job_platforms ?: []

                    def final_result = Result.SUCCESS
                    def continue_build = true

                    // Create and run combined bundles jobs first if enabled
                    if (use_separate_combined_job && combined_job_platforms.size() > 0) {
                        echo "Creating separate combined bundles jobs for platforms: ${combined_job_platforms}"

                        def combined_jobs = [:]
                        for (platform in combined_job_platforms) {
                            // Check if this platform has any combine variants in either frosty or patchfrosty matrix
                            def has_combine_variants = (frosty_matrix.any { matrix_platform ->
                                matrix_platform.name == platform && matrix_platform.variants?.any { variant ->
                                    variant.format?.contains('combine')
                                }
                            } || patchfrosty_matrix.any { matrix_platform ->
                                matrix_platform.name == platform && matrix_platform.variants?.any { variant ->
                                    variant.format?.contains('combine')
                                }
                            })

                            if (has_combine_variants) {
                                def combined_job_name = env.branch_name + '.combined_bundles.' + platform
                                def combined_job_args = args + [
                                    string(name: 'combine_code_changelist', value: combine_code_changelist),
                                    string(name: 'combine_data_changelist', value: combine_data_changelist),
                                ]

                                // Add baseline parameters for delta bundle creation in patchfrosty
                                def platform_modifiers = [platform]
                                def baseline_set = LibCommonNonCps.get_setting_value(branchfile.standard_jobs_settings, platform_modifiers, 'baseline_set', true)
                                def first_patch = LibCommonNonCps.get_setting_value(branchfile.standard_jobs_settings, platform_modifiers, 'first_patch', false)
                                if (baseline_set) {
                                    baseline_file = ReturnBaselineFile(project.name)
                                    def use_combined_baseline = branchfile.standard_jobs_settings.combine_bundles?.is_target_branch ?: false
                                    if (first_patch) {
                                        combined_job_args += ReturnDiscBaseline(baseline_file, env.patch_branch, platform, use_combined_baseline)
                                    } else {
                                        combined_job_args += ReturnPatchBaseline(baseline_file, env.patch_branch, platform, use_combined_baseline)
                                    }
                                }

                                combined_jobs[combined_job_name] = {
                                    def downstream_job = build(job: combined_job_name, parameters: combined_job_args, propagate: false)
                                    final_result = final_result.combine(Result.fromString(downstream_job.result))
                                    LibJenkins.printFailureMessage(this, downstream_job, false)
                                    LibJenkins.printRunningJobs(this)
                                }
                            }
                        }

                        if (combined_jobs.size() > 0) {
                            echo "Running ${combined_jobs.size()} combined bundles jobs in parallel"
                            parallel(combined_jobs)

                            // If any combined bundles job failed, fail the entire scheduler
                            if (final_result != Result.SUCCESS) {
                                echo 'Combined bundles jobs failed, aborting frosty/patchfrosty builds'
                                currentBuild.result = final_result.toString()
                                return
                            }
                            echo 'All combined bundles jobs completed successfully'
                        }
                    }
                    for (def run = 0; run <= env.retry_limit.toInteger(); run++) { // Retry failed jobs if retry_limit > 0.
                        jobs = [:]
                        final_result = Result.SUCCESS
                        for (platform in frosty_matrix) {
                            for (variant in platform.variants) {
                                def variant_args = args
                                def rebuild_combine_code_changelist = ''
                                def rebuild_combine_data_changelist = ''
                                if (variant.format.contains('combine')) {
                                    variant_args += [
                                        string(name: 'combine_code_changelist', value: combine_code_changelist),
                                        string(name: 'combine_data_changelist', value: combine_data_changelist),
                                    ]
                                    rebuild_combine_code_changelist = combine_code_changelist
                                    rebuild_combine_data_changelist = combine_data_changelist
                                }
                                Boolean allow_failure_frosty = variant.allow_failure ?: false
                                def job_name = env.branch_name + '.frosty.' + env.dataset + '.' + platform.name + '.' + variant.format + '.' + variant.region + '.' + variant.config
                                if (NeedsRebuildData(job_name, code_changelist, data_changelist, rebuild_combine_code_changelist, rebuild_combine_data_changelist)) {
                                    if (run > 0 && IsGameFailure(job_name)) {
                                        if (allow_failure_frosty == false) {
                                            final_result = Result.FAILURE
                                            // Set pipeline as failed if there are jobs from IsGameFailure category.
                                            continue_build = false
                                        }
                                        break
                                    } else {
                                        jobs[job_name] = {
                                            def downstream_job = build(job: job_name, parameters: variant_args, propagate: false)
                                            if (allow_failure_frosty == false) {
                                                final_result = final_result.combine(Result.fromString(downstream_job.result))
                                            }
                                            LibJenkins.printFailureMessage(this, downstream_job, allow_failure_frosty)
                                            LibJenkins.printRunningJobs(this)
                                        }
                                    }
                                }
                            }
                            if (continue_build == false) {
                                break
                            }
                        }
                        if (continue_build == false) {
                            break
                        }
                        for (platform in patchfrosty_matrix) {
                            def use_combined_baseline = branchfile.standard_jobs_settings.combine_bundles?.is_target_branch ?: false
                            baseline_file = ReturnBaselineFile(project.name)
                            def platform_args = args + ReturnDiscBaseline(baseline_file, env.patch_branch, platform.name, use_combined_baseline)

                            def use_dynamic_disc_baselines = branchfile.standard_jobs_settings.use_dynamic_disc_baselines ?: false
                            if (use_dynamic_disc_baselines) {
                                // Fetch code/data CLs from the store regular baseline job
                                def fetch_baseline_reference_job = branchfile.standard_jobs_settings.fetch_baseline_reference_job
                                def last_successful_code_build = LibJenkins.getLastStableCodeChangelist(fetch_baseline_reference_job)
                                def last_successful_data_build = LibJenkins.getLastStableDataChangelist(fetch_baseline_reference_job)

                                platform_args += [
                                    string(name: 'disc_code_changelist', value: last_successful_code_build),
                                    string(name: 'disc_data_changelist', value: last_successful_data_build),
                                ]

                                if (use_combined_baseline) {
                                    def last_successful_combine_code_build = LibJenkins.getEnvironmentForLastStableBuild(fetch_baseline_reference_job)?.combine_code_changelist
                                    def last_successful_combine_data_build = LibJenkins.getEnvironmentForLastStableBuild(fetch_baseline_reference_job)?.combine_data_changelist
                                    platform_args += [
                                        string(name: 'combine_disc_code_changelist', value: last_successful_combine_code_build),
                                        string(name: 'combine_disc_data_changelist', value: last_successful_combine_data_build),
                                    ]
                                }
                            }

                            for (variant in platform.variants) {
                                def variant_args = platform_args
                                def modifiers = [platform.name, variant.format, variant.config, variant.region]
                                def first_patch = LibCommonNonCps.get_setting_value(branchfile.standard_jobs_settings, modifiers, 'first_patch', false)
                                if (first_patch == false) {
                                    variant_args += ReturnPatchBaseline(baseline_file, env.patch_branch, platform.name, use_combined_baseline)
                                }
                                def rebuild_combine_code_changelist = ''
                                def rebuild_combine_data_changelist = ''
                                if (variant.format.contains('combine')) {
                                    variant_args += [
                                        string(name: 'combine_code_changelist', value: combine_code_changelist),
                                        string(name: 'combine_data_changelist', value: combine_data_changelist),
                                    ]
                                    rebuild_combine_code_changelist = combine_code_changelist
                                    rebuild_combine_data_changelist = combine_data_changelist
                                }
                                Boolean allow_failure = variant.allow_failure ?: false
                                def job_name = env.branch_name + '.patchfrosty.' + env.dataset + '.' + platform.name + '.' + variant.format + '.' + variant.region + '.' + variant.config
                                if (NeedsRebuildData(job_name, code_changelist, data_changelist, rebuild_combine_code_changelist, rebuild_combine_data_changelist)) {
                                    if (run > 0 && IsGameFailure(job_name)) {
                                        if (allow_failure == false) {
                                            final_result = Result.FAILURE
                                            // Set pipeline as failed if there are jobs from IsGameFailure category.
                                            continue_build = false
                                        }
                                        break
                                    } else {
                                        jobs[job_name] = {
                                            def downstream_job = build(job: job_name, parameters: variant_args, propagate: false)
                                            if (allow_failure == false) {
                                                final_result = final_result.combine(Result.fromString(downstream_job.result))
                                            }
                                            LibJenkins.printFailureMessage(this, downstream_job, allow_failure)
                                            LibJenkins.printRunningJobs(this)
                                        }
                                    }
                                }
                            }
                            if (continue_build == false) {
                                break
                            }
                        }
                        if (continue_build == false) {
                            break
                        }
                        parallel(jobs)
                        if (final_result == Result.SUCCESS) {
                            break
                        }
                    }
                    if (env.shift_every_build.toBoolean() == true && final_result == Result.SUCCESS) { // Only upload successful builds
                        build(job: env.branch_name + '.shift.upload', parameters: args, propagate: false, wait: false)
                    }
                    currentBuild.result = final_result.toString()

                    if (currentBuild.result.toString() == 'SUCCESS') {
                        // Update P4 counters value
                        if (env.enable_lkg_p4_counters.toBoolean() == true) {
                            //TODO: p4 counter -m 'dice'.${env.branch_name}.lkg.frosty.code ${code_changelist} 'dice'.${env.branch_name}.lkg.frosty.data ${data_changelist}
                            // e.g: dice.dev-na-battlefieldgame.lkg.frosty.code <code_changelist> dice.dev-na-battlefieldgame.lkg.frosty.data <data_changelist>
                            echo 'Trigger downstream job to update p4 counter:'
                            def counter_args = [
                                string(name: 'code_countername', value: LibCommonCps.getp4Countername(env.branch_name, 'lkg.frosty', branchfile, 'code')),
                                string(name: 'code_changelist', value: code_changelist),
                                string(name: 'data_countername', value: LibCommonCps.getp4Countername(env.branch_name, 'lkg.frosty', branchfile, 'data')),
                                string(name: 'data_changelist', value: data_changelist),
                            ]
                            build(job: env.branch_name + '.frosty.p4counterupdater', parameters: counter_args, propagate: false)
                        }

                        def patchfrosty_downstream_matrix = branchfile.patchfrosty_downstream_matrix
                        LibCommonCps.triggerDownstreamJobs(
                            this, patchfrosty_downstream_matrix, 'patchfrosty', env.branch_name, branchfile,
                            code_changelist, data_changelist, combine_code_changelist, combine_data_changelist
                        )
                    }

                    def slack_settings = branchfile.standard_jobs_settings?.slack_channel_patchfrosty
                    SlackMessageNew(currentBuild, slack_settings, project.short_name)

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
