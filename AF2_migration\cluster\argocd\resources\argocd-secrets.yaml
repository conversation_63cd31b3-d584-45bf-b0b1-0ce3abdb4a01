---
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: clustersetup-vaultstore
  namespace: argocd
spec:
  provider:
    vault:
      server: "https://ess.ea.com"
      path: "secrets/kv"
      namespace: "cds-dre-prod"
      auth:
        kubernetes:
          mountPath: "kubernetes/cobra/azure/dre-stockholm"
          role: cluster-setup
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  labels:
    app.kubernetes.io/name: argocd-secret
    app.kubernetes.io/part-of: argocd
  name: argocd-secret
  namespace: argocd
spec:
  secretStoreRef:
    name: clustersetup-vaultstore
    kind: SecretStore
  refreshInterval: "0"
  target:
    template:
      metadata:
        annotations:
          argocd.argoproj.io/compare-options: IgnoreExtraneous
  dataFrom:
    - extract:
        key: cobra/automation/kubernetes/azure/dre-stockholm/argocd/argocd-secret
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  labels:
    app.kubernetes.io/name: argocd-secret
    app.kubernetes.io/part-of: argocd
    argocd.argoproj.io/secret-type: repository
  name: gitlab-secret-dre-kubernetes
  namespace: argocd
spec:
  secretStoreRef:
    name: clustersetup-vaultstore
    kind: SecretStore
  refreshInterval: "0"
  target:
    template:
      metadata:
        annotations:
          argocd.argoproj.io/compare-options: IgnoreExtraneous
        labels:
          argocd.argoproj.io/secret-type: repo-creds
  dataFrom:
    - extract:
        key: cobra/automation/kubernetes/azure/dre-stockholm/argocd/gitlab-secret-dre-kubernetes


