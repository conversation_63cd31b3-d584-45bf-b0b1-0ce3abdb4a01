apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dice-kin-bilbo-eck-ingress
  annotations:
    traefik.ingress.kubernetes.io/router.entrypoints: websecure
    traefik.ingress.kubernetes.io/router.middlewares: traefik-https-redirect@kubernetescrd
spec:
  tls:
    - hosts:
        - dice-kin-bilbo-eck.cobra.dre.ea.com
  rules:
    - host: dice-kin-bilbo-eck.cobra.dre.ea.com
      http:
        paths:
          - backend:
              service:
                name: dice-kin-bilbo-eck-es-http
                port:
                  number: 9200
            path: /
            pathType: Prefix
