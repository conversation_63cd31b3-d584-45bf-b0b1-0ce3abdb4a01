---
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: cobra-loki-vaultstore
spec:
  provider:
    vault:
      server: "https://ess.ea.com"
      path: "secrets/kv"
      namespace: "cds-dre-prod"
      auth:
        kubernetes:
          mountPath: "kubernetes/cobra/azure/dre-stockholm"
          role: cluster-apps-observability
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  labels:
    app.kubernetes.io/name: cobra-loki-secrets
    app.kubernetes.io/part-of: cobra-loki
  name: cobra-loki-secrets
spec:
  secretStoreRef:
    name: cobra-loki-vaultstore
    kind: SecretStore
  refreshInterval: "12h"
  target:
    template:
      metadata:
        annotations:
          argocd.argoproj.io/compare-options: IgnoreExtraneous
        labels:
          argocd.argoproj.io/secret-type: repo-creds
  data:
    - secretKey: AZ_STORAGE_ACCOUNT_KEY
      remoteRef:
        key: cobra/automation/azure/dre-stockholm/observability/cobra-loki
        property: AZ_STORAGE_ACCOUNT_KEY1
    - secretKey: AZ_STORAGE_ACCOUNT_NAME
      remoteRef:
        key: cobra/automation/azure/dre-stockholm/observability/cobra-loki
        property: AZ_STORAGE_ACCOUNT_NAME
    - secretKey: AZ_LOGS_STORAGE_CONTAINER_NAME
      remoteRef:
        key: cobra/automation/azure/dre-stockholm/observability/cobra-loki
        property: AZ_LOGS_STORAGE_CONTAINER_NAME
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  labels:
    app.kubernetes.io/name: cobra-loki-ingress-secret
    app.kubernetes.io/part-of: cobra-loki
  name: cobra-loki-ingress-secret
spec:
  secretStoreRef:
    name: cobra-loki-vaultstore
    kind: SecretStore
  refreshInterval: "12h"
  target:
    template:
      metadata:
        annotations:
          argocd.argoproj.io/compare-options: IgnoreExtraneous
        labels:
          argocd.argoproj.io/secret-type: repo-creds
  data:
    - secretKey: users
      remoteRef:
        key: cobra/automation/azure/dre-stockholm/observability/ingress-auth-hashed
        property: users
