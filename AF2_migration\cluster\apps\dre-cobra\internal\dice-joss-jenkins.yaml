apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: dice-joss-jenkins
  namespace: argocd
  labels:
    project: internal
    group: jenkins
spec:
  project: internal
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/helm-charts/jenkins.git
    path: "."
    targetRevision: 0.9.15
    helm:
      values: |
        instanceName: dice-joss
        production: true
        enableBfaSlack: false
        createAgentListenerService: true
        serviceAccountName: internal
        jenkins:
          image: dre-docker-federated.artifacts.ea.com/cobra/jenkins/jenkins-cobra:2.479.3_fa3f874c
          resources:
            requests:
              cpu: 500m
              memory: 4Gi
            limits:
              cpu: 2
              memory: 4Gi
          storageSize: 60Gi
        etl:
          enabled: false
        elastic:
          url: http://dre-metrics-eck-es-http.internal.svc.cluster.local:9200
          indexPrefix: dst
        vault:
          role: cluster-apps-internal
          mountPath: kubernetes/cobra/azure/dre-stockholm
          mainAccountPath: cobra/automation/projects/cobra/accounts/main_account
          appRolePath: cobra/automation/projects/cobra/app_roles/jm-cobra-jenkins
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: internal
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
    # automated:
    #   prune: false
    #   selfHeal: false
