apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: escalator
  namespace: argocd
  labels:
    project: internal
    group: utility
spec:
  project: internal
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
    path: ./apps/dre-cobra/internal/escalator
    targetRevision: COBRA-7517-escalator
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: internal
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
    # TODO: Uncomment the automated block once app is deployed to production
    # automated:
    #   prune: true
    #   selfHeal: true
