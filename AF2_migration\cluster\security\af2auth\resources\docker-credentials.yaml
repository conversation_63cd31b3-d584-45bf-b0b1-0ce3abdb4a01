---
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: clustersetup-vaultstore
  namespace: kube-system
spec:
  provider:
    vault:
      server: "https://ess.ea.com"
      path: "secrets/kv"
      namespace: "cds-dre-prod"
      auth:
        kubernetes:
          mountPath: "kubernetes/cobra/azure/dre-stockholm"
          role: cluster-setup
---
apiVersion: external-secrets.io/v1beta1 
kind: ExternalSecret
metadata:
  name: dre-docker-federated-af-access
  namespace: kube-system
spec:
  secretStoreRef:
    name: clustersetup-vaultstore
    kind: SecretStore
  refreshInterval: "720h"
  target:
    template:
      type: kubernetes.io/dockerconfigjson
      data:
        .dockerconfigjson: "{{ .DOCKER_AUTH_CONFIG | toString }}"
      metadata:
        annotations:
          reflector.v1.k8s.emberstack.com/reflection-allowed: "true"
          reflector.v1.k8s.emberstack.com/reflection-auto-enabled: "true"
  data:
    - secretKey: DOCKER_AUTH_CONFIG
      remoteRef:
        key: artifacts/automation/dre-docker-federated/ro
        property: DOCKER_AUTH_CONFIG
---
apiVersion: mutations.gatekeeper.sh/v1
kind: ModifySet
metadata:
  name: dre-docker-federated-af-access
spec:
  applyTo:
    - groups: [""]
      kinds: ["Pod"]
      versions: ["v1"]
  location: "spec.imagePullSecrets"
  parameters:
    values:
      fromList:
        - name: dre-docker-federated-af-access
