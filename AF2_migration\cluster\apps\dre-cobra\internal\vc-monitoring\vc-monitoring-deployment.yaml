apiVersion: apps/v1
kind: Deployment
metadata:
  name: vc-monitoring
  labels:
    app: vc-monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: vc-monitoring
  template:
    metadata:
      labels:
        app: vc-monitoring
    spec:
      containers:
      - name: vc-monitoring
        image: dreeu-docker-local.artifactory.eu.ea.com/cobra/vc-monitoring:latest
        imagePullPolicy: "Always"
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        envFrom:
          - secretRef:
              name: vc-monitoring-secrets
