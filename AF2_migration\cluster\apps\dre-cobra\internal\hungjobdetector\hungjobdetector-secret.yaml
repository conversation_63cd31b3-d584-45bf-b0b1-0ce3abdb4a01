---
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: hungjobdetector-vaultstore
spec:
  provider:
    vault:
      server: "https://ess.ea.com"
      path: "secrets/kv"
      namespace: "cds-dre-prod"
      auth:
        kubernetes:
          mountPath: "kubernetes/cobra/azure/dre-stockholm"
          role: cluster-apps-internal

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  labels:
    app.kubernetes.io/name: hungjobdetector-service-account
    app.kubernetes.io/part-of: hungjobdetector
  name: hungjobdetector-service-account
spec:
  secretStoreRef:
    name: hungjobdetector-vaultstore
    kind: SecretStore
  refreshInterval: "12h"
  target:
    template:
      metadata:
        annotations:
          argocd.argoproj.io/compare-options: IgnoreExtraneous
        labels:
          argocd.argoproj.io/secret-type: repo-creds
  data:
    - secretKey: SLACK_CHANNEL_ID
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: hungjobdetector_slack_channel
    - secretKey: SLACK_TOKEN
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: hungjobdetector_slack_token
    - secretKey: JENKINS_USERNAME
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: username
    - secretKey: JENKINS_PASSWORD
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: password
