---
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: loki-log-parser-vaultstore
spec:
  provider:
    vault:
      server: "https://ess.ea.com"
      path: "secrets/kv"
      namespace: "cds-dre-prod"
      auth:
        kubernetes:
          mountPath: "kubernetes/cobra/azure/dre-stockholm"
          role: cluster-apps-internal

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  labels:
    app.kubernetes.io/name: loki-log-parser-secrets
    app.kubernetes.io/part-of: loki-log-parser
  name: loki-log-parser-secrets
spec:
  secretStoreRef:
    name: loki-log-parser-vaultstore
    kind: SecretStore
  refreshInterval: "12h"
  target:
    template:
      metadata:
        annotations:
          argocd.argoproj.io/compare-options: IgnoreExtraneous
        labels:
          argocd.argoproj.io/secret-type: repo-creds
  data:
    - secretKey: LOKI_USER_PASSWORD
      remoteRef:
        key: cobra/automation/loki_log_parser
        property: LOKI_USER_PASSWORD
