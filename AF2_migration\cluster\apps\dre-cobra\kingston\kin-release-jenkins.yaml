apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: kin-release-jenkins
  namespace: argocd
  labels:
    project: kingston
    group: jenkins
spec:
  project: kingston
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/helm-charts/jenkins.git
    path: "."
    targetRevision: 0.9.15
    helm:
      values: |
        instanceName: kin-release
        createAgentListenerService: true
        serviceAccountName: kingston
        noPluginsRedeploy: true
        jenkins:
          image: dre-docker-federated.artifacts.ea.com/cobra/jenkins/jenkins-kin:2.479.3_fa3f874c
          resources:
            requests:
              cpu: 500m
              memory: 8Gi
            limits:
              cpu: 2
              memory: 8Gi
          storageSize: 120Gi
        elastic:
          url: https://dice-metrics-eck.cobra.dre.ea.com
          indexPrefix: kingston
        vault:
          mountPath: kubernetes/cobra/azure/dre-stockholm
          role: cluster-apps-kingston
          mainAccountPath: cobra/automation/projects/kingston/accounts/main_account
          appRolePath: cobra/automation/projects/kingston/app_roles/jm-dice-kin
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: kingston
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
  #   automated:
  #     prune: true
  #     selfHeal: true
