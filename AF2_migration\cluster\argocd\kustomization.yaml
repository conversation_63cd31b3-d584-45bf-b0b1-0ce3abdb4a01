apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- https://kustomize:<EMAIL>/DRE/kubernetes/cluster-base.git//argocd?ref=master
- ./resources/argocd-secrets.yaml
- ./resources/argocd-ingress.yaml


# # changes to config maps
patchesStrategicMerge:
- overlays/configmap.yaml
- overlays/rbac-permissions.yaml
- overlays/notification-cm.yaml

patches:
- target:
    kind: ClusterRole
    name: argocd-server
  path: overlays/role.yaml