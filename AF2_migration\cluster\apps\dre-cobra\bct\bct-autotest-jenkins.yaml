apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: bct-autotest-jenkins
  namespace: argocd
  labels:
    project: bct
    group: jenkins
spec:
  project: bct
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/helm-charts/jenkins.git
    path: "."
    targetRevision: 0.9.15
    helm:
      values: |
        instanceName: bct-autotest
        production: true
        enableBfaSlack: false
        createAgentListenerService: true
        serviceAccountName: bct
        noPluginsRedeploy: true
        jenkins:
          image: dre-docker-federated.artifacts.ea.com/cobra/jenkins/jenkins-bct-autotest:2.479.3_fa3f874c
          javaOpts: >-
            -XX:InitialRAMPercentage=50.0
            -XX:MaxRAMPercentage=50.0
            -Djava.rmi.server.hostname=localhost
            -Dcom.sun.management.jmxremote.port=51000
            -Dcom.sun.management.jmxremote.rmi.port=51000
            -Dcom.sun.management.jmxremote.local.only=false
            -Dcom.sun.management.jmxremote.authenticate=false
            -Dcom.sun.management.jmxremote.ssl=false
          resources:
            requests:
              cpu: 500m
              memory: 16Gi
            limits:
              cpu: 4
              memory: 16Gi
          storageSize: 120Gi
        elastic:
          url: http://dice-metrics-eck-es-http.dice.svc.cluster.local:9200
          indexPrefix: bct
        vault:
          role: cluster-apps-bct
          mountPath: kubernetes/cobra/azure/dre-stockholm
          mainAccountPath: cobra/automation/projects/bct/accounts/main_account
          appRolePath: cobra/automation/projects/bct/app_roles/jm-bct
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: bct
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
  #   automated:
  #     prune: true
  #     selfHeal: true
