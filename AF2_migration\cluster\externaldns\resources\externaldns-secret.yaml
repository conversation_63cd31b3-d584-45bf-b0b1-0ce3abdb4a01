---
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: clustersetup-vaultstore
  namespace: externaldns
spec:
  provider:
    vault:
      server: "https://ess.ea.com"
      path: "secrets/kv"
      namespace: "cds-dre-prod"
      auth:
        kubernetes:
          mountPath: "kubernetes/cobra/azure/dre-stockholm"
          role: cluster-setup
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: externaldns-secret
  namespace: externaldns
spec:
  secretStoreRef:
    name: clustersetup-vaultstore
    kind: SecretStore
  refreshInterval: "0"
  target:
    template:
      metadata:
        annotations:
          argocd.argoproj.io/compare-options: IgnoreExtraneous
  dataFrom:
    - extract:
        key: iac/automation/route53/dre.ea.com
