apiVersion: kibana.k8s.elastic.co/v1
kind: Kibana
metadata:
  name: bct-bilbo-kibana
spec:
  version: 6.8.23
  count: 1
  elasticsearchRef:
    name: bct-bilbo-eck
    namespace: bct
  podTemplate:
    spec:
      containers:
        - name: kibana
          resources:
            requests:
              cpu: 500m
              memory: 1Gi
            limits:
              cpu: 2
              memory: 2.5Gi
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /
              port: 5601
              scheme: HTTPS
  config:
    xpack.security.enabled: false
