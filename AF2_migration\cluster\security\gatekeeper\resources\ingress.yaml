apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  namespace: gatekeeper-system
  name: gatekeeper
  annotations:
    # Use SSL
    traefik.ingress.kubernetes.io/router.entrypoints: web,websecure
    traefik.ingress.kubernetes.io/router.middlewares: "traefik-https-redirect@kubernetescrd"
spec:
  tls:
    - hosts:
        - gatekeeper-aks.cobra.dre.ea.com
  rules:
    - host: gatekeeper-aks.cobra.dre.ea.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: gatekeeper-policy-manager
                port:
                  name: http

