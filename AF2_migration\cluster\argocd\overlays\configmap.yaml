apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-cm
data:
  resource.customizations: |
    admissionregistration.k8s.io/MutatingWebhookConfiguration:
      ignoreDifferences: |
        jsonPointers:
        - /webhooks/0/clientConfig/caBundle
  statusbadge.enabled: "true"
  admin.enabled: "false"
  url: https://argocd-dice.cobra.dre.ea.com
  users.anonymous.enabled: "true"
  exec.enabled: "true"
  accounts.backstage: apiKey
  accounts.backstage.enabled: "true"
  dex.config: |
    connectors:
      - type: ldap
        # Required field for connector id.
        id: ldap
        # Required field for connector name.
        name: LDAP
        config:
          # Host and optional port of the LDAP server in the form "host:port".
          # If the port is not supplied, it will be guessed based on "insecureNoSSL",
          # and "startTLS" flags. 389 for insecure or StartTLS connections, 636
          # otherwise.
          host: sto-adsdce02-t0.dice.ad.ea.com:3268

          # Following field is required if the LDAP host is not using TLS (port 389).
          # Because this option inherently leaks passwords to anyone on the same network
          # as dex, THIS OPTION MAY BE REMOVED WITHOUT WARNING IN A FUTURE RELEASE.
          #
          insecureNoSSL: true
          # If a custom certificate isn't provide, this option can be used to turn on
          # TLS certificate checks. As noted, it is insecure and shouldn't be used outside
          # of explorative phases.
          #
          insecureSkipVerify: true
          # When connecting to the server, connect using the ldap:// protocol then issue
          # a StartTLS command. If unspecified, connections will use the ldaps:// protocol
          #
          # startTLS: true
          # Path to a trusted root certificate file. Default: use the host's root CA.
          #rootCA: /etc/dex/ldap.ca
          # A raw certificate file can also be provided inline.
          #rootCAData:
          # The DN and password for an application service account. The connector uses
          # these credentials to search for users and groups. Not required if the LDAP
          # server provides access for anonymous auth.
          # Please note that if the bind password contains a `$`, it has to be saved in an
          # environment variable which should be given as the value to `bindPW`.
          bindDN: $dex.bind_dn
          bindPW: $dex.bind_password

          # User search maps a username and password entered by a user to a LDAP entry.
          userSearch:
            # BaseDN to start the search from. It will translate to the query
            # "(&(objectClass=person)(uid=<username>))".
            baseDN: DC=ad,DC=ea,DC=com
            # Optional filter to apply when searching the directory.
            filter: "(objectClass=user)"
            # username attribute used for comparing user entries. This will be translated
            # and combine with the other filter as "(<attr>=<username>)".
            username: userPrincipalName
            # The following three fields are direct mappings of attributes on the user entry.
            # String representation of the user.
            idAttr: userPrincipalName
            # Required. Attribute to map to Email.
            emailAttr: userPrincipalName
            # Maps to display name of users. No default value.
            nameAttr: userPrincipalName

          # Group search queries for groups given a user entry.
          groupSearch:
            # BaseDN to start the search from. It will translate to the query
            # "(&(objectClass=group)(member=<user uid>))".
            baseDN: OU=Groups,DC=ad,DC=ea,DC=com
            # Optional filter to apply when searching the directory.
            filter: "(&(objectclass=group)(|(name=*DRE.*)(name=*dre-*)))"
            # Following two fields are used to match a user to a group. It adds an additional
            # requirement to the filter that an attribute in the group must match the user's
            # attribute value.
            userAttr: distinguishedName
            groupAttr: "member:1.2.840.113556.1.4.1941:"
            # Represents group name.`
            nameAttr: name
