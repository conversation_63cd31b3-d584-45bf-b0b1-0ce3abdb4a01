apiVersion: "apps/v1"
kind: DaemonSet
metadata:
  name: sync-sysctl
  namespace: kube-system
spec:
  selector:
    matchLabels:
      name: sync-sysctl
  template:
    metadata:
      labels:
        name: sync-sysctl
    spec:
      containers:
        - name: sysctl
          image: docker.artifacts.ea.com/busybox:latest
          resources:
            limits:
              cpu: "10m"
              memory: "64Mi"
            requests:
              cpu: "10m"
              memory: "64Mi"
          securityContext:
            privileged: true
          command:
            - "/bin/sh"
            - "-c"
            - |
              set -o errexit
              set -o xtrace
              sysctl -w vm.max_map_count=1677720
              sleep infinity
