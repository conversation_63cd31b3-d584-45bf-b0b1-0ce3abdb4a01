---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dre-telemetry-kibana-ingress
  annotations:
    traefik.ingress.kubernetes.io/router.entrypoints: websecure
    traefik.ingress.kubernetes.io/router.middlewares: traefik-https-redirect@kubernetescrd
spec:
  tls:
    - hosts:
        - dre-telemetry-kibana.cobra.dre.ea.com
  rules:
    - host: dre-telemetry-kibana.cobra.dre.ea.com
      http:
        paths:
          - backend:
              service:
                name: dre-telemetry-kibana-kb-http
                port:
                  number: 5601
            path: /
            pathType: Prefix
