package com.ea.matrixfiles

import com.ea.lib.model.autotest.AutotestCategory
import com.ea.lib.model.autotest.FrostedAutotestCategory
import com.ea.lib.model.autotest.Name
import com.ea.lib.model.autotest.Platform
import com.ea.lib.model.autotest.Region
import com.ea.lib.model.autotest.TestInfo
import com.ea.lib.model.autotest.TestSuite

/**
 * See <a href='https://docs.google.com/document/d/1pTxwXhm0T5Mstbg4uaBkjWruC5ntz7pZxqGsmBEcTwk/preview'>Flow Overview</a>
 * See <a href='https://docs.google.com/document/d/1mTfOtPPX93529M7EgmmaGBpmcuMoDyhZ7ZlYCKoVUzw/preview'>Jenkins Config</a>
 **/
class BctAutotestMatrix extends AutotestMatrix {
    private static final String TRUNK_CODE_DEV = 'trunk-code-dev'
    private static final String TRUNK_CONTENT_DEV = 'trunk-content-dev'
    private static final String DEV_NA_TO_TRUNK = 'dev-na-to-trunk'
    private static final String TRUNK_TO_DEV_NA = 'trunk-to-dev-na'
    private static final String TRUNK_CODE_DEV_SANITIZERS = 'trunk-code-dev-sanitizers'
    private static final String DEV_NA_TO_TRUNK_SUB = 'dev-na-to-trunk-sub'
    private static final String TASK1 = 'task1'

    /* ********** ********** ********** ********** **********
   Some default values that can be used across lots of tests
    */
    private static final List EXTRA_ARGS_V1 = ['--rest-port', '5143', '--timeout-client-level-load', '00:15:00', '--runtime-connect-timeout', 360, '--timeout-client-deploy', '01:10:00', '--atf-rest-heartbeat-enabled', 'true', '--xbsx-skip-defer-exec', 'true']
    private static final List EXTRA_ARGS_V2 = ['--timeout-client-level-load', '00:12:00', '--runtime-connect-timeout', 360, '--timeout-client-deploy', '01:10:00', '--atf-rest-heartbeat-enabled', 'true', '--xbsx-skip-defer-exec', 'true', '--kill-subprocesses-on-exit', 'true']
    private static final List EXTRA_ARGS_V6 = ['--timeout-client-level-load', '00:12:00', '--runtime-connect-timeout', 360, '--ps5-deployment-method', 'Push', '--timeout-client-deploy', '01:10:00', '--atf-rest-heartbeat-enabled', 'true', '--xbsx-skip-defer-exec', 'true', '--kill-subprocesses-on-exit', 'true']
    private static final List EXTRA_ARGS_SANITIZERS_UNITTESTS = ['--timeout-client-level-load', '00:12:00', '--runtime-connect-timeout', 360, '--ps5-deployment-method', 'Push', '--timeout-client-deploy', '01:10:00', '--atf-rest-heartbeat-enabled', 'true', '--xbsx-skip-defer-exec', 'true', '--kill-subprocesses-on-exit', 'true', '--process-timeout', '1200', '--heartbeat-timeout', '180']
    private static final List EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING = [
        '--use-remote-file-share-handling', true,
        '--use-force-file-share-cleanup', true,
    ]
/*
    private static final List EXTRA_ARGS_CRITERION =[
        '--atf-endpoint-list', 'windows:dice-qve-atf-01;xb1:dice-qve-atf-01;ps4:dice-qve-atf-01;xbsx:dice-qve-atf-01;ps5:dice-qve-atf-01;linux:dice-qve-atf-01',
    ]
*/
    private static final List<Platform> BCT_PLATFORMS = [
        new Platform(name: Name.PS5),
        new Platform(name: Name.WIN64),
        new Platform(name: Name.XBSX),
    ]

    private static final List<Platform> REGIONAL_BCT_PLATFORMS = [
        new Platform(name: Name.PS5, region: Region.DEV),
        new Platform(name: Name.WIN64, region: Region.WW),
        new Platform(name: Name.XBSX, region: Region.WW),
    ]

    private final Map bctSlackSettings = [
        channels                  : ['#bf-sqr-bct-notify'],
        skip_for_multiple_failures: false,
    ]

    /* ********** ********** ********** ********** **********
    Define sets of tests to run for different types of autotests.
    Add more setups here if needed.
    */

    private final TestInfo frostedtests_fbapi = new TestInfo(
        testGroup: 'frostedtests_fbapi',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        platforms: [new Platform(name: Name.WIN64)],
        testNames: ['frosted_scene_workflow_tests', 'frosted_efw_core_workflow_tests'],
        extraArgs: ['--superbundles', 'false', '--fbapi-test-frosted-args', '"-includeTestDataModules true"', '--fbapi-test-frosted-launch-wait-seconds', 480, '--fbapi-test-pytest-test-timeout-seconds', 360, '--fbapi-test-running-timeout-seconds', 1200],
        poolType: '',
    )

    private final TestInfo frostedTestsTrunkCodeDev = new TestInfo(
        testGroup: 'frostedtests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        platforms: [new Platform(name: Name.WIN64)],
        testNames: [
            'FrostEdTest_BFWorkflowsTechArt',
        ],
        extraArgs: ['--frosted-runtime-args', '"-includeTestDataModules true -QV.Enable false"', '--frosted-launch-timeout', 600, '--frosted-test-timeout', 480, '--alltests', '--databaseId', "BattlefieldGameData.${TRUNK_CODE_DEV}.Win32.Debug"],
        poolType: '',
    )

    private final TestInfo frostedTestsTrunkToDevNa = new TestInfo(
        testGroup: 'frostedtests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        platforms: [new Platform(name: Name.WIN64)],
        testNames: [
            'FrostEdTest_BFWorkflowsTechArt',
        ],
        extraArgs: ['--frosted-runtime-args', '"-includeTestDataModules true -QV.Enable false"', '--frosted-launch-timeout', 600, '--frosted-test-timeout', 480, '--alltests', '--databaseId', "BattlefieldGameData.${TRUNK_TO_DEV_NA}.Win32.Debug"],
        poolType: '',

    )

    private final TestInfo frostedTestsTrunkCodeDevExtended = new TestInfo(
        testGroup: 'frostedtests_extended',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        platforms: [new Platform(name: Name.WIN64)],
        tests: [
            new TestSuite(
                name: 'frostedtest_EcsOrbitalPerformance',
                extraArgs: ['--frosted-runtime-args', '"-includeTestDataModules true -QV.Enable false"', '--frosted-launch-timeout', 600, '--frosted-test-timeout', 3000, '--frosted-test-timeout', 3000, '--frosted-telemetry-threshold-ms', 1, '--frosted-test-additional-options', '"ForwardFrostEdPerfDataToReport:true"', '--alltests', '--ensemble-grpc', 'true', '--clean-index', 'true', '--databaseId', "BattlefieldGameData.${TRUNK_CODE_DEV}.Win32.Debug"],
                poolType: ''
            ),
        ]
    )

    private final TestInfo frostedTestsDevNaToTrunk = new TestInfo(
        testGroup: 'frostedtests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        platforms: [new Platform(name: Name.WIN64)],
        installPypiwin32: true,
        testNames: [
            'FrostEdTest_BFWorkflowsTechArt',
        ],
        extraArgs: ['--frosted-runtime-args', '"-includeTestDataModules true -QV.Enable false"', '--frosted-launch-timeout', 600, '--frosted-test-timeout', 480, '--alltests', '--databaseId', "BattlefieldGameData.${DEV_NA_TO_TRUNK}.Win32.Debug"],
        poolType: '',
    )

    private final TestInfo lkg_qv_trunk = new TestInfo(
        parallelLimit: 3,
        runLevelsInParallel: true,
        testGroup: 'lkg_qv',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 9,
        testNames: ['LKG_QV', 'LKG_AutoPlayers'],
        extraArgs: EXTRA_ARGS_V1,
        platforms: BCT_PLATFORMS,
    )

    private final TestInfo lkg_qv_win = new TestInfo(
        parallelLimit: 1,
        runLevelsInParallel: true,
        testGroup: 'lkg_qv',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 2,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        tests: [
            new TestSuite(
                name: 'LKG_AutoPlayers',
                extraArgs: (EXTRA_ARGS_V1 + ['--override-server-build-configuration', 'final']).flatten(),
                needGameServer: true
            ),
        ]
    )

    private final TestInfo lkg_auto = new TestInfo(
        parallelLimit: 6,
        hasPinnedAgents: true,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkg_auto',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        testNames: ['LKG_CoreCombat', 'LKG_MMT', 'LKG_F2P', 'LKG_SP'],
        extraArgs: EXTRA_ARGS_V1,
        platforms: BCT_PLATFORMS,
    )

    private final TestInfo lkg_auto_nonpinned_agents = new TestInfo(
        parallelLimit: 3,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkg_auto',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        testNames: ['LKG_CoreCombat', 'LKG_MMT', 'LKG_F2P', 'LKG_SP'],
        extraArgs: EXTRA_ARGS_V1,
        platforms: BCT_PLATFORMS,
    )

    private final TestInfo lkg_checkmate = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
        ],
        parallelLimit: 2,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkgcheckmate',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        tests: [
            new TestSuite(name: 'CheckMate_Mandatory_TestSuite', extraArgs: EXTRA_ARGS_V1, needGameServer: false)
        ]
    )

    private final TestInfo lkg_checkmate_pinned = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
        ],
        parallelLimit: 2,
        hasPinnedAgents: true,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkgcheckmate',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 4,
        numTestRuns: 3,
        tests: [
            new TestSuite(name: 'CheckMate_Mandatory_TestSuite', extraArgs: EXTRA_ARGS_V1, needGameServer: false)
        ]
    )

    private final TestInfo unittests = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        parallelLimit: 1,
        hasPinnedAgents: true,
        runLevelsInParallel: true,
        testGroup: 'unittests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        useLatestDrone: true,
        tests: [
            new TestSuite(name: 'AutoPlayersCodeTests', extraArgs: [EXTRA_ARGS_V2, '--max-parallel-tests', 1].flatten()),
        ]
    )

    private final TestInfo unittests_asan_tool = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64DLL, region: Region.WW),
        ],
        parallelLimit: 2,
        hasPinnedAgents: false,
        runLevelsInParallel: true,
        testGroup: 'unittests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        useLatestDrone: true,
        testNames: ['BFUnitTests', 'EngineUnitTests'],
        extraArgs: [EXTRA_ARGS_SANITIZERS_UNITTESTS].flatten(),
        fetchTests: true,
        poolType: '',
    )

    private final TestInfo unittests_asan_static = new TestInfo(
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        parallelLimit: 4,
        hasPinnedAgents: false,
        runLevelsInParallel: true,
        testGroup: 'unittests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        useLatestDrone: true,
        testNames: ['BFUnitTests', 'EngineUnitTests'],
        extraArgs: [EXTRA_ARGS_SANITIZERS_UNITTESTS, '--max-parallel-tests', 1].flatten(),
        fetchTests: true,
    )

    private final TestInfo unittests_ubsan_static = new TestInfo(
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        parallelLimit: 2,
        hasPinnedAgents: false,
        runLevelsInParallel: true,
        testGroup: 'unittests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        useLatestDrone: true,
        testNames: ['BFUnitTests', 'EngineUnitTests'],
        extraArgs: [EXTRA_ARGS_SANITIZERS_UNITTESTS, '--max-parallel-tests', 1].flatten(),
        fetchTests: true,
    )

    private final TestInfo unittests_engine = new TestInfo(
        platforms: [
            new Platform(name: Name.TOOL, region: Region.WW)
        ],
        parallelLimit: 1,
        hasPinnedAgents: true,
        runLevelsInParallel: true,
        testGroup: 'unittests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        useLatestDrone: true,
        tests: [
            new TestSuite(name: 'EngineUnitTests', extraArgs: [EXTRA_ARGS_V6, '--max-parallel-tests', 1].flatten(), poolType: ''),
        ]
    )

    private final TestInfo pt_func_playtest = new TestInfo(
        platforms: REGIONAL_BCT_PLATFORMS,
        parallelLimit: 3,
        runLevelsInParallel: true,
        testGroup: 'pt_func_playtest',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 9,
        tests: [
            new TestSuite(name: 'Systemic_PlaytestValidation', extraArgs: EXTRA_ARGS_V2),
        ]
    )

    private final TestInfo lkg_bootanddeploy = new TestInfo(
        parallelLimit: 3,
        hasPinnedAgents: true,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkg_bootanddeploy',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        testNames: ['LKG_BootAndDeploy'],
        extraArgs: [EXTRA_ARGS_V1, '--max-parallel-tests', 2].flatten(),
        platforms: REGIONAL_BCT_PLATFORMS,
    )

    private final TestInfo lkg_bootanddeploy_tool = new TestInfo(
        parallelLimit: 3,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkg_bootanddeploy',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        testNames: ['LKG_BootAndDeploy'],
        extraArgs: EXTRA_ARGS_V1,
        platforms: [
            new Platform(name: Name.TOOL, region: Region.WW)
        ],
    )

    private final TestInfo trunk_asan = new TestInfo(
        platforms: REGIONAL_BCT_PLATFORMS,
        parallelLimit: 3,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        tests: [
            new TestSuite(name: 'AsanTestSuite',
                platforms: [new Platform(name: Name.XBSX), new Platform(name: Name.WIN64)],
                extraArgs: ['--runtime-connect-timeout', 360, '--ps5-run-options', 'flexibleMemory:2048', '--crash-dump-timeout', 6000, '--timeout-client-level-load', '01:00:00', '--timeout-server-level-load', '01:00:00', '--level-load-timeout', 600, '--timeout-client-start', '00:30:00', '--xbsx-skip-defer-exec', 'true']),
            new TestSuite(name: 'AsanTestSuite',
                platforms: [new Platform(name: Name.PS5)],
                extraArgs: ['--runtime-connect-timeout', 360, '--max-parallel-tests', 1, '--ps5-run-options', 'flexibleMemory:2048', '--crash-dump-timeout', 6000, '--timeout-client-level-load', '01:00:00', '--timeout-server-level-load', '01:00:00', '--level-load-timeout', 600, '--timeout-client-start', '00:30:00', '--timeout-client-deploy', '01:10:00']),
        ]
    )

    private final TestInfo performance_medium_priority = new TestInfo(
        parallelLimit: 3,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 9,
        platforms: REGIONAL_BCT_PLATFORMS,
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Perf_Flythrough', extraArgs: EXTRA_ARGS_V6),
        ]
    )

    private final TestInfo pt_perf_performance_setup = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        testNames: ['F2P_Perf_Features', 'SP_Perf_Flythrough', 'MP_Perf_Flythrough'],
        extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_code_dev_performance_setup = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        testNames: ['MP_Perf_Gameplay:AutoPlayTest_MP_Abbasid_Conquest0_Fidelity:AutoPlayTest_MP_Abbasid_Conquest0_Performance'],
        extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_performance_code_dev_setup = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        testNames: ['F2P_Perf_Gameplay:F2P_Perf_Gameplay_MPGranite_GraniteSquad0_Fidelity:F2P_Perf_Gameplay_MPGranite_GraniteSquad0_Performance'],
        extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_final_code_dev_setup = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        testNames: ['MP_Perf_Flythrough_Detailed:Flythrough_MP_Abbasid'],
        extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_highpriority_performance_setup = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'F2P_Perf_Flythrough',
            'F2P_Perf_Gameplay',
            'SP_Perf_Gameplay',
            'F2P_Perf_Gameplay_GauntletComparingLayouts',
            'F2P_Perf_Gameplay_GauntletOnLargest',
            'MP_Perf_Gameplay',
        ],
        extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_highpriority_performance_setup_win = new TestInfo(
        parallelLimit: 1,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 3,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        testNames: ['F2P_Perf_Features', 'SP_Perf_Flythrough', 'MP_Perf_Flythrough'],
        extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_highpriority_flythroughs_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 2,
        runLevelsInParallel: true,
        timeoutHours: 11,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Mem_Gameplay', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true'].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_highpriority_flythroughs_final_setup2 = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 2,
        runLevelsInParallel: true,
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Mem_Gameplay', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_flythroughs_final_setup_dev_na = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 15,
        platforms: REGIONAL_BCT_PLATFORMS,
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'SP_Mem_Gameplay_FirstBatch', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200].flatten(), needGameServer: true),
            new TestSuite(name: 'SP_Mem_Gameplay_SecondBatch', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_flythroughs_minspec_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 3,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Perf_Flythrough_MinSpec_Detailed', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_minspec_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 19,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Perf_Flythrough_MinSpec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'SP_Perf_Flythrough_MinSpec', timeoutHours: 1, extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, '--override-server-build-configuration', 'final'].flatten(), needGameServer: false),
            new TestSuite(name: 'F2P_Perf_Features_MinSpec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'F2P_Perf_Gameplay_GauntletComparingLayouts_MinSpec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'F2P_Perf_Gameplay_GauntletOnLargest_MinSpec', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
            new TestSuite(name: 'F2P_Perf_Gameplay:F2P_Perf_Gameplay_MPGranite_Gauntlet_Performance_gla_min_spec:F2P_Perf_Gameplay_MPGranite_Gauntlet_CacheGrab_Performance_gla_min_spec:F2P_Perf_Gameplay_MPGranite_Gauntlet_BeaconRecovery_Performance_gla_min_spec:F2P_Perf_Gameplay_MPGranite_Gauntlet_DataExtraction_Performance_gla_min_spec:F2P_Perf_Gameplay_MPGranite_Gauntlet_Standoff_Performance_gla_min_spec:F2P_Perf_Gameplay_MPGranite_Gauntlet_Vendetta_Performance_gla_min_spec:', timeoutHours: 2, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_minspec_trunk_to_dev_na_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 3,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: ['F2P_Perf_Gameplay_MinSpec', 'MP_Perf_Gameplay_MinSpec'],
        extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 2, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_flythroughs_recspec_final_setup_dev_na = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 2,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: ['MP_Mem_Gameplay_RecSpec', 'F2P_Mem_Gameplay_RecSpec'],
        extraArgs: [EXTRA_ARGS_V6, '--test-state-change-timeout', 120, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_lowpriority_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 2,
        runLevelsInParallel: true,
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        testNames: ['MP_Perf_LoadLevel', 'F2P_Perf_LoadLevel'],
        extraArgs: [EXTRA_ARGS_V6, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_lowpriority_final_setup_synced = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Perf_Gameplay', timeoutHours: 3, extraArgs: EXTRA_ARGS_V6, needGameServer: true),
            new TestSuite(name: 'Common_Perf_CoreCombat', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true'].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_lowpriority_final_setup_synced_ps5 = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'Common_Perf_CoreCombat', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V6, 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true'].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo frosted_vstests = new TestInfo(
        testGroup: 'frostedtests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 10,
        platforms: [new Platform(name: Name.WIN64)],
        useLatestDrone: true,
        tests: [
            new TestSuite(name: 'AllVSTests', extraArgs: ['--max-parallel-tests', 1].flatten(), fetchTests: true, poolType: ''),
        ]
    )

    /* ********** ********** ********** ********** **********
    Collect the available setups for the different types of tests, and connect them to the branches that will use these.
    */

    private final Map collectingSlackSettings = [
        (DEFAULT)                  : [channels: [], skip_for_multiple_failures: true],
        (TRUNK_CODE_DEV)           : bctSlackSettings,
        (TRUNK_CONTENT_DEV)        : bctSlackSettings,
        (DEV_NA_TO_TRUNK)          : bctSlackSettings,
        (TRUNK_TO_DEV_NA)          : bctSlackSettings,
        (TRUNK_CODE_DEV_SANITIZERS): bctSlackSettings,
        (TASK1)                    : bctSlackSettings,
        (DEV_NA_TO_TRUNK_SUB)      : bctSlackSettings,
    ]

    /* ********** ********** ********** ********** **********
    Define if the levels should be tested in parallel or not.
    */

    private final Map runLevelsInParallel = [
        (DEFAULT)                  : true,
        (TRUNK_CODE_DEV)           : false,
        (TRUNK_CONTENT_DEV)        : false,
        (DEV_NA_TO_TRUNK)          : false,
        (TRUNK_TO_DEV_NA)          : false,
        (TRUNK_CODE_DEV_SANITIZERS): false,
        (TASK1)                    : false,
        (DEV_NA_TO_TRUNK_SUB)      : false,
    ]

    @Override
    List<String> getBranches() {
        return [TRUNK_CODE_DEV, TRUNK_CONTENT_DEV, DEV_NA_TO_TRUNK, TRUNK_TO_DEV_NA, TRUNK_CODE_DEV_SANITIZERS, TASK1, DEV_NA_TO_TRUNK_SUB]
    }

    @Override
    List<AutotestCategory> getTestCategories() {
        return [
            new FrostedAutotestCategory(
                name: 'frostedtests',
                testDefinition: 'frostedtests',
                runBilbo: false,
                needGameServer: false,
                uploadJournals: false,
                branches: [
                    branchConfiguration(TRUNK_CODE_DEV, frostedTestsTrunkCodeDev, 'H 6,16 * * 1-7'),
                    branchConfiguration(DEV_NA_TO_TRUNK, frostedTestsDevNaToTrunk, 'H 4,12,18 * * 1-7'),
                    branchConfiguration(TRUNK_TO_DEV_NA, frostedTestsTrunkToDevNa, 'H 10,14,22 * * 1-7'),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, frostedTestsDevNaToTrunk, ''),
                ]
            ),
            new FrostedAutotestCategory(
                name: 'frostedtests_extended',
                testDefinition: 'frostedtests_extended',
                runBilbo: false,
                needGameServer: false,
                uploadJournals: false,
                branches: [
                    branchConfiguration(TRUNK_CODE_DEV, frostedTestsTrunkCodeDevExtended, 'H 0,18,21 * * 1-7'),
                    branchConfiguration(DEV_NA_TO_TRUNK, frostedTestsTrunkCodeDevExtended, 'H 14 * * 1-7'),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, frostedTestsTrunkCodeDevExtended, ''),
                ]
            ),/*
            new AutotestCategory(
                name: 'pt_stab',
                testDefinition: 'pt_stab',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(DEV_NA_TO_TRUNK, pt_stab_dev_na_to_trunk, 'H H/12 * * 1-7'),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, pt_stab_dev_na_to_trunk, ''),
                ]
            ),*/
            new AutotestCategory(
                name: 'unittests',
                testDefinition: 'unittests',
                runBilbo: false,
                buildType: 'dll',
                config: 'release',
                elipyCmd: 'icepick_run_codetests',
                needGameServer: false,
                uploadJournals: false,
                captureVideo: false,
                branches: [
                    branchConfiguration(DEV_NA_TO_TRUNK, unittests, 'H 2,6,15 * * 1-7'),
                    branchConfiguration(TRUNK_TO_DEV_NA, unittests, 'H 5,8,15 * * 1-7'),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, unittests, ''),
                ]
            ),
            new AutotestCategory(
                name: 'unittests_asan_tool',
                testDefinition: 'unittests_asan_tool',
                runBilbo: false,
                buildType: 'dll',
                config: 'release',
                elipyCmd: 'icepick_run_codetests',
                needGameServer: false,
                uploadJournals: false,
                captureVideo: false,
                useExistingFilerBuild: true,
                customTag: 'asan',
                branches: [
                    branchConfiguration(TRUNK_CODE_DEV_SANITIZERS, unittests_asan_tool, 'H 0,4,8,12,16,20 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'unittests_asan_static',
                testDefinition: 'unittests_asan_static',
                runBilbo: false,
                config: 'final',
                elipyCmd: 'icepick_run_codetests',
                needGameServer: false,
                uploadJournals: false,
                captureVideo: false,
                useExistingFilerBuild: true,
                customTag: 'asan',
                branches: [
                    branchConfiguration(TRUNK_CODE_DEV_SANITIZERS, unittests_asan_static, 'H 0,4,9,14,18,21 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'unittests_ubsan_static',
                testDefinition: 'unittests_ubsan_static',
                runBilbo: false,
                config: 'final',
                elipyCmd: 'icepick_run_codetests',
                needGameServer: false,
                uploadJournals: false,
                captureVideo: false,
                useExistingFilerBuild: true,
                customTag: 'ubsan',
                branches: [
                    branchConfiguration(TRUNK_CODE_DEV_SANITIZERS, unittests_ubsan_static, 'H H/4 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'unittests_engine',
                testDefinition: 'unittests_engine',
                runBilbo: false,
                buildType: 'dll',
                config: 'release',
                elipyCmd: 'icepick_run_codetests',
                needGameServer: false,
                uploadJournals: false,
                captureVideo: false,
                branches: [
                    branchConfiguration(DEV_NA_TO_TRUNK, unittests_engine, 'H 0,8,20 * * 1-7'),
                    branchConfiguration(TRUNK_TO_DEV_NA, unittests_engine, 'H 0,3,21 * * 1-7'),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, unittests_engine, 'H 4,13,23 * * 1-7'),
                    branchConfiguration(TRUNK_CODE_DEV, unittests_engine, 'H 1,7,17 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'pt_func_playtest',
                testDefinition: 'pt_func_playtest',
                runBilbo: true,
                needGameServer: true,
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(DEV_NA_TO_TRUNK, pt_func_playtest, 'H 2,16,19 * * 1-7'),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, pt_func_playtest, ''),
                ]
            ),
            new AutotestCategory(
                name: 'trunk_asan',
                testDefinition: 'trunk_asan',
                runBilbo: true,
                needGameServer: false,
                uploadJournals: false,
                captureVideo: true,
                customTag: 'asan',
                branches: [
                    branchConfiguration(TRUNK_CODE_DEV_SANITIZERS, trunk_asan, '')
                ]
            ),
            new AutotestCategory(
                name: 'lkg_qv',
                testDefinition: 'lkg_qv',
                enableP4Counters: true,
                runBilbo: true,
                needGameServer: true,
                uploadJournals: true,
                captureVideo: true,
                branches: [
                    branchConfiguration(TRUNK_CODE_DEV, lkg_qv_trunk, ''),
                    branchConfiguration(DEV_NA_TO_TRUNK, lkg_qv_trunk, 'H 1,7 * * 1-7'),
                    branchConfiguration(TRUNK_CONTENT_DEV, lkg_qv_trunk, ''),
                    branchConfiguration(TRUNK_TO_DEV_NA, lkg_qv_trunk, 'H 9,16,19 * * 1-7'),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, lkg_qv_trunk, ''),

                ]
            ),
            new FrostedAutotestCategory(
                name: 'frostedtests_fbapi',
                testDefinition: 'frostedtests_fbapi',
                runBilbo: false,
                needGameServer: false,
                uploadJournals: false,
                branches: [
                    branchConfiguration(TRUNK_CODE_DEV, frostedtests_fbapi, 'H 12,14 * * 1-7'),
                    branchConfiguration(DEV_NA_TO_TRUNK, frostedtests_fbapi, 'H 17 * * 1-7'),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, frostedtests_fbapi, ''),
                ]
            ),
            new AutotestCategory(
                name: 'lkg_qv_win',
                testDefinition: 'lkg_qv_win',
                enableP4Counters: true,
                runBilbo: true,
                needGameServer: true,
                uploadJournals: true,
                captureVideo: true,
                branches: [
                    branchConfiguration(TRUNK_CODE_DEV, lkg_qv_win, ''),
                ]
            ),
            new AutotestCategory(
                name: 'lkg_auto',
                testDefinition: 'lkg_auto',
                enableP4Counters: true,
                runBilbo: true,
                needGameServer: true,
                uploadJournals: true,
                captureVideo: true,
                branches: [
                    branchConfiguration(TRUNK_CODE_DEV, lkg_auto, ''),
                    branchConfiguration(TRUNK_CONTENT_DEV, lkg_auto, ''),
                    branchConfiguration(DEV_NA_TO_TRUNK, lkg_auto_nonpinned_agents, 'H 0,6,11,16 * * 1-7'),
                    branchConfiguration(TRUNK_TO_DEV_NA, lkg_auto_nonpinned_agents, 'H 6,17 * * 1-7'),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, lkg_auto_nonpinned_agents, ''),
                ]
            ),
            new AutotestCategory(
                name: 'lkg_bootanddeploy',
                testDefinition: 'lkg_bootanddeploy',
                enableP4Counters: true,
                runBilbo: true,
                uploadJournals: true,
                captureVideo: true,
                branches: [
                    branchConfiguration(TRUNK_CODE_DEV, lkg_bootanddeploy, 'H 11,20 * * 1-7'),
                    branchConfiguration(TRUNK_CONTENT_DEV, lkg_bootanddeploy, 'H 9,19 * * 1-7'),
                    branchConfiguration(TRUNK_TO_DEV_NA, lkg_bootanddeploy, 'H 5,23 * * 1-7'),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, lkg_bootanddeploy, 'H 15,22 * * 1-7'),
                    branchConfiguration(DEV_NA_TO_TRUNK, lkg_bootanddeploy, 'H 12,3 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'lkg_bootanddeploy_tool',
                testDefinition: 'lkg_bootanddeploy_tool',
                enableP4Counters: true,
                runBilbo: true,
                buildType: 'dll',
                config: 'release',
                uploadJournals: true,
                captureVideo: true,
                branches: [
                    branchConfiguration(TRUNK_CODE_DEV, lkg_bootanddeploy_tool, 'H 6,9,12,15,18,22,1,3 * * 1-7'),
                    branchConfiguration(TRUNK_CONTENT_DEV, lkg_bootanddeploy_tool, 'H 4,9,6,15 * * 1-7'),
                    branchConfiguration(TRUNK_TO_DEV_NA, lkg_bootanddeploy_tool, 'H 0,3,6,9,12,15,18,21 * * 1-7'),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, lkg_bootanddeploy_tool, 'H 1,4,7,10,13,16,19,22 * * 1-7'),
                    branchConfiguration(DEV_NA_TO_TRUNK, lkg_bootanddeploy_tool, 'H 5,10,13,16,20,23 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'lkg_checkmate',
                testDefinition: 'lkg_checkmate',
                enableP4Counters: true,
                registerVerifiedForPreflight: true,
                config: 'release',
                runBilbo: true,
                registerSmoke: true,
                needGameServer: false,
                uploadJournals: true,
                captureVideo: true,
                buildType: 'dll',
                branches: [
                    branchConfiguration(TRUNK_CODE_DEV, lkg_checkmate_pinned, ''),
                    branchConfiguration(TRUNK_CONTENT_DEV, lkg_checkmate_pinned, ''),
                    branchConfiguration(DEV_NA_TO_TRUNK, lkg_checkmate, 'H 0,22 * * 1-7'),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, lkg_checkmate, ''),
                ]
            ),
            new AutotestCategory(
                name: 'lkg_checkmate_final',
                testDefinition: 'lkg_checkmate_final',
                enableP4Counters: true,
                config: 'release',
                runBilbo: true,
                needGameServer: false,
                uploadJournals: true,
                captureVideo: true,
                buildType: 'dll',
                branches: [
                    branchConfiguration(TRUNK_TO_DEV_NA, lkg_checkmate, 'H 3,13 * * 1-7'),
                ]
            ),
            new AutotestCategory(
                name: 'pt_perf_performance_setup',
                testDefinition: 'pt_perf_performance_setup',
                runBilbo: true,
                needGameServer: true,
                remoteLabel: 'eala',
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(DEV_NA_TO_TRUNK, pt_perf_performance_setup, 'H 8,16 * * 1-6'),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, pt_perf_performance_setup, ''),
                    branchConfiguration(TRUNK_CODE_DEV, pt_perf_code_dev_performance_setup, 'H 11 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'pt_perf_highpriority_performance_setup',
                testDefinition: 'pt_perf_highpriority_performance_setup',
                runBilbo: true,
                needGameServer: true,
                remoteLabel: 'eala',
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(DEV_NA_TO_TRUNK, pt_perf_highpriority_performance_setup, 'H 8 * * 1-6'),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, pt_perf_highpriority_performance_setup, ''),
                    branchConfiguration(TRUNK_CODE_DEV, pt_perf_performance_code_dev_setup, 'H 17 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'pt_perf_highpriority_performance_setup_win',
                testDefinition: 'pt_perf_highpriority_performance_setup_win',
                runBilbo: true,
                needGameServer: true,
                remoteLabel: 'eala',
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(DEV_NA_TO_TRUNK, pt_perf_highpriority_performance_setup_win, 'H 16 * * 1-6'),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, pt_perf_highpriority_performance_setup_win, ''),
                ]),
            new AutotestCategory(
                name: 'pt_perf_highpriority_flythroughs_final_setup',
                testDefinition: 'pt_perf_highpriority_flythroughs_final_setup',
                runBilbo: true,
                needGameServer: true,
                remoteLabel: 'eala',
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(DEV_NA_TO_TRUNK, pt_perf_highpriority_flythroughs_final_setup, 'H 10,18 * * 1-6'),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, pt_perf_highpriority_flythroughs_final_setup, ''),
                    branchConfiguration(TRUNK_CODE_DEV, pt_perf_final_code_dev_setup, 'H 11 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'pt_perf_highpriority_flythroughs_final_setup2',
                testDefinition: 'pt_perf_highpriority_flythroughs_final_setup2',
                runBilbo: true,
                needGameServer: true,
                remoteLabel: 'eala',
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(DEV_NA_TO_TRUNK, pt_perf_highpriority_flythroughs_final_setup2, 'H 1 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'pt_perf_flythroughs_final_setup',
                testDefinition: 'pt_perf_flythroughs_final_setup',
                runBilbo: true,
                needGameServer: true,
                remoteLabel: 'eala',
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(DEV_NA_TO_TRUNK, pt_perf_flythroughs_final_setup_dev_na, 'H 20 * * 1-6'),
                ]),
            new AutotestCategory(
                name: 'performance_medium_priority',
                testDefinition: 'performance_medium_priority',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(TRUNK_TO_DEV_NA, performance_medium_priority, 'H 0 * * 1-7'),
                ]),
            new AutotestCategory(
                name: 'pt_perf_flythroughs_recspec_final_setup',
                testDefinition: 'pt_perf_flythroughs_recspec_final_setup',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(DEV_NA_TO_TRUNK, pt_perf_flythroughs_recspec_final_setup_dev_na, 'H 16 * * 1-7'),
                ]),
            new AutotestCategory(
                name: 'performance_minspec_final',
                testDefinition: 'performance_minspec_final',
                runBilbo: true,
                needGameServer: true,
                remoteLabel: 'eala',
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(DEV_NA_TO_TRUNK, pt_perf_flythroughs_minspec_final_setup, 'H 21 * * 7'),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, pt_perf_flythroughs_minspec_final_setup, ''),
                ]),
            new AutotestCategory(
                name: 'pt_perf_minspec_performance_setup',
                testDefinition: 'pt_perf_minspec_performance_setup',
                runBilbo: true,
                needGameServer: true,
                remoteLabel: 'eala',
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(DEV_NA_TO_TRUNK, pt_perf_minspec_performance_setup, 'H 2 * * 7'),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, pt_perf_minspec_performance_setup, ''),
                ]),
            new AutotestCategory(
                name: 'pt_perf_minspec_trunk_to_dev_na_final_setup',
                testDefinition: 'pt_perf_minspec_trunk_to_dev_na_final_setup',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(TRUNK_TO_DEV_NA, pt_perf_minspec_trunk_to_dev_na_final_setup, 'H 3 * * 7'),
                ]),
            new AutotestCategory(
                name: 'pt_perf_lowpriority_final_setup',
                testDefinition: 'pt_perf_lowpriority_final_setup',
                runBilbo: true,
                needGameServer: true,
                remoteLabel: 'eala',
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(DEV_NA_TO_TRUNK, pt_perf_lowpriority_final_setup, 'H 13 * * 1-7'),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, pt_perf_lowpriority_final_setup, ''),
                ]),/*
            new AutotestCategory(
                name: 'pt_perf_lowpriority_XBSS_performance_setup',
                testDefinition: 'pt_perf_lowpriority_XBSS_performance_setup',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(DEV_NA_TO_TRUNK, pt_perf_lowpriority_XBSS_performance_setup, ''),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, pt_perf_lowpriority_XBSS_performance_setup, 'H 5 * * 1-7'),
                ]),
            new AutotestCategory(
                name: 'pt_perf_lowpriority_XBSS_final_setup',
                testDefinition: 'pt_perf_lowpriority_XBSS_final_setup',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(DEV_NA_TO_TRUNK, pt_perf_lowpriority_XBSS_final_setup, ''),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, pt_perf_lowpriority_XBSS_final_setup, 'H 0 * * 1-7'),
                ]),*/
            new AutotestCategory(
                name: 'pt_perf_lowpriority_final_setup_synced',
                testDefinition: 'pt_perf_lowpriority_final_setup_synced',
                runBilbo: true,
                needGameServer: true,
                remoteLabel: 'eala',
                config: 'final',
                serverConfig: 'final',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: false,
                branches: [
                    branchConfiguration(DEV_NA_TO_TRUNK, pt_perf_lowpriority_final_setup_synced, 'H 10 * * 1-7'),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, pt_perf_lowpriority_final_setup_synced, ''),
                ]),
            new AutotestCategory(
                name: 'pt_perf_lowpriority_final_setup_synced_ps5',
                testDefinition: 'pt_perf_lowpriority_final_setup_synced_ps5',
                remoteLabel: 'eala',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: false,
                branches: [
                    branchConfiguration(DEV_NA_TO_TRUNK, pt_perf_lowpriority_final_setup_synced_ps5, 'H 20 * * 1-7'),
                    branchConfiguration(DEV_NA_TO_TRUNK_SUB, pt_perf_lowpriority_final_setup_synced_ps5, ''),
                ]),
            new FrostedAutotestCategory(
                name: 'frosted_vstests',
                testDefinition: 'frosted_vstests',
                runBilbo: false,
                needGameServer: false,
                uploadJournals: false,
                useExistingFilerBuild: true,
                branches: [
                    branchConfiguration(TRUNK_CODE_DEV, frosted_vstests, 'H 4,18 * * 1-7'),
                    branchConfiguration(DEV_NA_TO_TRUNK, frosted_vstests, ''),
                    branchConfiguration(TRUNK_TO_DEV_NA, frosted_vstests, 'H 2,6,18 * * 1-7'),
                ]),
        ]
    }

    @Override
    List<AutotestCategory> getManualTestCategories() {
        return [
            new AutotestCategory(
                name: 'lkg_qv',
                testDefinition: 'lkg_qv',
                runBilbo: false,
                needGameServer: true,
                uploadJournals: false,
                branches: [
                    branchConfiguration(TRUNK_TO_DEV_NA, lkg_qv_trunk, ''),
                    branchConfiguration(DEV_NA_TO_TRUNK, lkg_qv_trunk, ''),
                    branchConfiguration(TRUNK_CODE_DEV, lkg_qv_trunk, ''),
                ],
                isManual: true
            ),
        ]
    }

    @Override
    Map<String, List<Platform>> getPlatforms() {
        return [
            (DEFAULT)                  : [new Platform(name: Name.WIN64)],
            (TRUNK_CODE_DEV)           : BCT_PLATFORMS,
            (TRUNK_CONTENT_DEV)        : BCT_PLATFORMS,
            (DEV_NA_TO_TRUNK)          : BCT_PLATFORMS,
            (TRUNK_TO_DEV_NA)          : BCT_PLATFORMS,
            (TRUNK_CODE_DEV_SANITIZERS): BCT_PLATFORMS,
            (TASK1)                    : BCT_PLATFORMS,
            (DEV_NA_TO_TRUNK_SUB)      : BCT_PLATFORMS,
        ]
    }

    @Override
    boolean shouldLevelsRunInParallel(String branchName) {
        return getSetting(branchName, runLevelsInParallel)
    }

    @Override
    Map getSlackSettings(String branchName) {
        return (Map) getSetting(branchName, collectingSlackSettings)
    }

    @Override
    Map getManualTestCategoriesSetting(String branchName) {
        Map settings = [
            parallel_limit: 1,
            categories    : getManualTestCategories(branchName),
        ]
        return settings
    }
}
