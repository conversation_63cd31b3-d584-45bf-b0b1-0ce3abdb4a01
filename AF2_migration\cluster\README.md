# DRE Stockholm Cluster Setup

- [Purpose](#purpose)
- [Adding new cluster wide components](#adding-new-cluster-wide-components)
  - [Usage](#usage)
    - [ArgoCD](#argocd)
      - [cluster-projects.yaml](#cluster-projectsyaml)
      - [cluster-applications.yaml](#cluster-applicationsyaml)
      - [argocd/resources/argocd-ingress.yaml](#argocdresourcesargocd-ingressyaml)
      - [argocd/resources/argocd-secrets.yaml](#argocdresourcesargocd-secretsyaml)
      - [argocd/overlays/configmap.yaml](#argocdoverlaysconfigmapyaml)
      - [argocd/overlays/rbac-permissions.yaml](#argocdoverlaysrbac-permissionsyaml)
    - [Traefik](#traefik)
      - [traefik/overlays/traefik-ingress.yaml](#traefikoverlaystraefik-ingressyaml)
      - [traefik/overlays/tls-wildcard.yaml](#traefikoverlaystls-wildcardyaml)
    - [ExternalDNS](#externaldns)
      - [externaldns/overlays/deployment.yaml](#externaldnsoverlaysdeploymentyaml)
      - [externaldns/resources/externaldns-secret.yaml](#externaldnsresourcesexternaldns-secretyaml)
    - [Metrics](#metrics)
    - [Applications](#applications)
- [Deployment](#deployment)
- [Extras](#extras)
  - [Adding a new namespace](#adding-a-new-namespace)
  - [Adding a new Cobra application](#adding-a-new-cobra-application)

## Local repo structure

Given the fact this repo is called "cluster", it can be easily conflicted with other cluster configuration. Therefore, it's
recommended to use a local folder structure like

```
kubernetes/azure/dre-stockholm
    -> repo for cluster creation written in Terraform
kubernetes/azure/dre-stockholm/cluster
    -> repo for team/studio/location specific cluster configuration (this repo)
kubernetes/
    -> repo for standard DRE cluster configuration (cluster-base)
```

## Purpose

This repo is a fork of [cluster-example](https://gitlab.ea.com/DRE/kubernetes/clusters/example-cluster).
When paired with the [cluster-base](https://gitlab.ea.com/DRE/kubernetes/cluster-base) repo it deploys the unique _cluster_ wide specific components of _DRE Kubernetes Standard_ set of applications.

These cluster specific components include things such as: located based ingress FQDN, location specific datastore usage, etc.

Additional cluster wide components that are not apart of the DRE standard deployment set should also be deployed to the kubernetes clusters via this repo.

An example of this would be if your team wanted to install a new kubernetes operator.

## Adding new cluster wide components

If your team requires an application that you feel would be beneficial to everyone then please submit an MR to [cluster-base](https://gitlab.ea.com/DRE/kubernetes/cluster-base) with its generic deployment manifest and create a separate MR on this repo with the documentation outlining how to provide deployment specific overrides.


4. Update the following files

#### ArgoCD

##### [cluster-projects.yaml](cluster-projects.yaml)

This file creates an ArgoCD [project](https://argoproj.github.io/argo-cd/user-guide/projects/) within ArgoCD that will be used to contain all the cluster-wide applications setup by this repo. For an example of available options reference the online example [here](https://argoproj.github.io/argo-cd/operator-manual/project.yaml).

- Replace `<location>` to the studio location where the kubernetes cluster lives: eav, tib, etc. or AWS, GCP, Azure for the cloud
- Replace `<team>` with the team that will own this kubernetes cluster

##### [cluster-applications.yaml](cluster-applications.yaml)

This file creates an ArgoCD [application](https://argoproj.github.io/argo-cd/operator-manual/declarative-setup/#applications). For an example of available options reference the online example [here](https://argoproj.github.io/argo-cd/operator-manual/application.yaml).

- Replace `<location>` to the studio location where the kubernetes cluster lives: eav, tib, etc. or AWS, GCP, Azure for the cloud
- Replace `<team>` with the team that will own this kubernetes cluster

##### [argocd/resources/argocd-ingress.yaml](argocd/resources/argocd-ingress.yaml)

This file updates the ArgoCD ingress object with an updated entry specific to this cluster. This DNS entry needs to be unique.

Set the _spec.tls.hosts_ and the _spec.rules.host_ entries to whatever DNS entry you want to access the ArgoCD UI from.

##### [argocd/resources/argocd-secrets.yaml](argocd/resources/argocd-secrets.yaml)

This file contains 2 `ExternalSecret` objects that will generate Kubernetes Secrets that ArgoCD will consume.

The `argocd-secret` object contains all the secrets required by ArgoCD Server. These values are passed to the ArgoCD Server config via token interpolation by ArgoCD itself.

The secret in ESS must be a json blob that contains the following:

```json
{
  "admin.password": "<local admin account password in bcrypt format. See: https://www.browserling.com/tools/bcrypt on how to create a bcrypt encrypted password>",
  "admin.passwordMtime": "<Time of this deployment in YYYY-MM-DDTHH:MM:SSZ format>",
  "dex.bind_dn": "<Distinguished Name of the LDAP user account>",
  "dex.bind_password": "Password for the Account above",
  "server.secretkey": "<Unique GUID to identity this deployment of ArgoCD>"
}
```

Update:

- _spec.vaultMountPoint_ to the mount point specific to your kubernetes cluster
- _spec.dataFrom_ to the path for the secret entry containing the json blob from above.

The `gitlab-secret-dre-kubernetes` object contains the username and password used by argo when attempting to clone repos from under https://gitlab.ea.com/dre/kubernetes/*.

To generate this credential:

1. Navigate to: https://gitlab.ea.com/groups/DRE/kubernetes/-/settings/repository
2. Provide the name of this cluster as the name of the deploy token, with `-argocd` as a suffix. Eg: `eav-dre-argocd`
3. Leave expiration blank
4. Provide a simple user name. (Do not use symbols)
5. Ensure that **read_repository** is checked
6. Store the output in ESS as:

```json
{
  "deployToken": "<token>",
  "username": "<username>"
}
```

Update:

- _spec.vaultMountPoint_ to the mount point specific to your kubernetes cluster
- _spec.dataFrom_ to the path for the secret entry containing the json blob from above.

##### [argocd/overlays/configmap.yaml](argocd/overlays/configmap.yaml)

This is the main configuration file for the ArgoCD server. The file will pull in and reference the secret values directly from the `argocd-secret`'s properties. Eg: `dex.bind_dn` is accessed in the config file as `$dex.bind_dn`.

**NOTE:** If a team wishes to be able to deploy from more than the https://gitlab.ea.com/DRE/Kubernetes/* subgroup then they can create more ExternalSecret objects and add their references to the _data."repository.credentials"_ array in this file.

Update:

- _data.url_ to match the URL declared within the [ArgoCD Ingress object](argocd/resources/argocd-ingress.yaml)

##### [argocd/overlays/rbac-permissions.yaml](argocd/overlays/rbac-permissions.yaml)

This file contains the Role Based Access Control (RBAC) setup for ArgoCD. For online help refer to [here](https://github.com/argoproj/argo-cd/blob/master/docs/operator-manual/rbac.md)

Update:

- Update the `<AD Group>` entry with the AD group that should have administrative access when using the LDAP SSO.

#### Traefik

##### [traefik/overlays/traefik-ingress.yaml](traefik/overlays/traefik-ingress.yaml)

This file updates the Traefik ingress object with an updated entry specific to this cluster. This DNS entry needs to be unique.

Set the _spec.tls.hosts_ and the _spec.rules.host_ entries to whatever DNS entry you want to access the Traefik Management UI from

##### [traefik/overlays/tls-wildcard.yaml](traefik/overlays/tls-wildcard.yaml)

Update:

- _spec.vaultMountPoint_ to the mount point specific to your kubernetes cluster

**NOTE:** The default provided path will fetch the EA Internal wildcard certificate for the *.dre.ea.com DNS suffix. Do not change this unless you know what you are doing

#### ExternalDNS

##### [externaldns/overlays/deployment.yaml](externaldns/overlays/deployment.yaml)

We need to update our deployment of ExternalDNS so we will be able to tag all of the DNS entries created by this instance as owned by this instance.

Update:

- _spec.template.spec.containers.env.name[EXTERNAL\_DNS\_TXT\_OWNER\_ID].value_ to the cluster's name. Eg: `eav-dre`

##### [externaldns/resources/externaldns-secret.yaml](externaldns/resources/externaldns-secret.yaml)

Update:

- _spec.vaultMountPoint_ to the mount point specific to your kubernetes cluster
- _spec.dataFrom_ to the path for the secret entry containing the json blob from above.

#### Metrics

TODO: WIP section.

#### Applications

Update all the yaml files under the `apps\cluster` folder to update their `repoURL` to point to this forked repo's url

## Deployment

To deploy this repo:

1. Ensure your kubectl context is pointing at the correct kubernetes cluster
2. Call the following from the root folder of this repo to verify the manifests

    ```bash
    kustomize build .
    ```

3. Call the following to deploy this repo to the cluster

    ```bash
    kustomize build . | kubectl apply -f -
    ```

    - It is possible that a race condition occurs during deployment were CRDS are not processed by the time kubectl sends the manifests. When this occurs the above command will error our saying it does not know about objects types _???_. To resolve this issue, re-run the above command

## Extras

### Adding a new namespace

To add a new namespace to be managed by this repository you need to follow a few simple steps. In the following snippets, a placedholder `<example>` will be used to indicate where you should replace fields with your own values.

1. Add a new `Application` manifest to the `apps/` directory. The follow snippet is an example, fields where the `<example>` placeholder appear should be replaced with your desired values.

    ```yaml
    apiVersion: argoproj.io/v1alpha1
    kind: Application
    metadata:
      name: admin-<example>
      # You'll usually want to add your resources to the argocd namespace.
      namespace: argocd
      # Add a this finalizer ONLY if you want these to cascade delete.
    spec:
      # The project the application belongs to.
      project: cluster-administration

      # Source of the application manifests
      source:
        repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/eav/dre/cluster.git
        targetRevision: master
        path: apps/<example>

        # directory
        directory:
          recurse: true

      # Destination cluster and namespace to deploy the application
      destination:
        server: https://kubernetes.default.svc
        namespace: argocd

      # Sync policy
      syncPolicy:
        automated:
          prune: true
    ```

2. Add a new directory at `apps/<example>`
3. In the `apps/<example>` directory add the following files:
   - `apps/<example>/<example>-application.yaml`

    ```yaml
    apiVersion: argoproj.io/v1alpha1
    kind: Application
    metadata:
      name: <example>-applications
      namespace: argocd
      finalizers:
      - resources-finalizer.argocd.argoproj.io
    spec:
      project: argocd-<example>
      source:
        repoURL: 'https://gitlab.ea.com/DRE/kubernetes/clusters/eav/dre/<example>/argocd-<example>.git'
        path: .
        targetRevision: HEAD
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: ie-etl
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
    ```

   - `apps/<example>/<example>-namespace.yaml`

    ```yaml
    apiVersion: v1
    kind: Namespace
    metadata:
      name: <example>
    ```

   - `apps/<example>/<example>-project.yaml`

    ```yaml
    apiVersion: argoproj.io/v1alpha1
    kind: AppProject
    metadata:
      name: argocd-<example>
      namespace: argocd
    spec:
      # Project description
      description: <example description>

      # Allow manifests to deploy from any Git repos
      sourceRepos:
      - 'https://gitlab.ea.com/DRE/**'

      # Only permit applications to deploy to the ie namespace in the same cluster
      destinations:
      - namespace: <example>
        server: https://kubernetes.default.svc
      - namespace: argocd # This allows anything in this project to deploy new applications
        server: https://kubernetes.default.svc
    ```

4. Update the ArgoCD RBAC configuration to allow the new application to deploy to the cluster.
   - `argocd/overlays/rbac-permissions.yaml`

    ```yaml
    ... # Other roles
    # <example> project
    p, role:argocd-<example>, applications, create, argocd-<example>/*, allow
    p, role:argocd-<example>, applications, delete, argocd-<example>/*, allow
    p, role:argocd-<example>, applications, get, argocd-<example>/*, allow
    p, role:argocd-<example>, applications, override, argocd-<example>/*, allow
    p, role:argocd-<example>, applications, sync, argocd-<example>/*, allow
    p, role:argocd-<example>, applications, update, argocd-<example>/*, allow
    p, role:argocd-<example>, projects, get, argocd-<example>, allow
    g, <ldap group associated with example>, role:argocd-<example>
    ```

### Adding a new Cobra application

You must follow a few simple steps to add a new cobra service/application that this repository will manage. In the following snippets, a placeholder `<example>` will be used to indicate where you should replace fields with your values.

It's necessary to have a repository with all Kubernetes manifests before creating this new application. E.g: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/dre-cobra/kingston.git

1. Add a new `Application` manifest to the `apps/dre-cobra/<project>` directory. The following snippet is an example. Fields where the `<example>` placeholder appears should be replaced with your desired values.

    ```yaml
    apiVersion: argoproj.io/v1alpha1
    kind: Application
    metadata:
      name: <example>
      # You'll usually want to add your resources to the argocd namespace.
      namespace: argocd
      # Add a this finalizer ONLY if you want these to cascade delete.
    spec:
      # The project the application belongs to.
      project: <project>

      # Source of the application manifests
      source:
        repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/dre-cobra/<project>.git
        targetRevision: master
        path: <example>

        # directory
        directory:
          recurse: true

      # Destination cluster and namespace to deploy the application
      destination:
        server: https://kubernetes.default.svc
        namespace: <project>

      # Sync policy
      syncPolicy:
        automated:
          prune: true
          selfHeal: true # ArgoCD will try to keep this application always running
    ```

With this file pushed to `master` ArgoCD will fetch all manifests and will try to deploy the application. Log in using LDAP on [ArgoCD page](https://argo.cobra.dre.ea.com/).

