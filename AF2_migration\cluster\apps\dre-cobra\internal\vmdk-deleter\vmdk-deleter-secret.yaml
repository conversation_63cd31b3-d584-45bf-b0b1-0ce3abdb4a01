---
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: vmdk-deleter-vaultstore
spec:
  provider:
    vault:
      server: "https://ess.ea.com"
      path: "secrets/kv"
      namespace: "cds-dre-prod"
      auth:
        kubernetes:
          mountPath: "kubernetes/cobra/azure/dre-stockholm"
          role: cluster-apps-internal

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  labels:
    app.kubernetes.io/name: vmdk-deleter-secrets
    app.kubernetes.io/part-of: vmdk-deleter
  name: vmdk-deleter-secrets
spec:
  secretStoreRef:
    name: vmdk-deleter-vaultstore
    kind: SecretStore
  refreshInterval: "12h"
  target:
    template:
      metadata:
        annotations:
          argocd.argoproj.io/compare-options: IgnoreExtraneous
        labels:
          argocd.argoproj.io/secret-type: repo-creds
  data:
    - secretKey: VSPHERE_USER
      remoteRef:
        key: cobra/automation/vsphere/global
        property: EMAIL
    - secretKey: VSPHERE_PASSWORD
      remoteRef:
        key: cobra/automation/vsphere/global
        property: PASSWORD
    - secretKey: BOT_SLACK_TOKEN
      remoteRef:
        key: cobra/automation/slack/dre_cobra_slack_bot
        property: SLACK_TOKEN
