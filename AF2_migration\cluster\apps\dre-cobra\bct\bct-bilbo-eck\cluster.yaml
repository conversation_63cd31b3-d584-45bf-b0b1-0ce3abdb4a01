---
apiVersion: elasticsearch.k8s.elastic.co/v1
kind: Elasticsearch
metadata:
  name: bct-bilbo-eck
spec:
  version: 6.8.23
  http:
    tls:
      selfSignedCertificate:
        disabled: true
  volumeClaimDeletePolicy: DeleteOnScaledownOnly
  nodeSets:
    - name: masters
      count: 3
      podTemplate:
        spec:
          containers:
            - name: elasticsearch
              resources:
                requests:
                  cpu: 500m
                  memory: 4Gi
                limits:
                  memory: 4Gi
              env:
                # heap size: https://www.elastic.co/guide/en/elasticsearch/reference/current/advanced-configuration.html#set-jvm-heap-size
                - name: ES_JAVA_OPTS
                  value: -Xms2g -Xmx2g
      config:
        node.master: true
        node.data: true
        node.ingest: false
        bootstrap.memory_lock: false
        xpack.security.authc:
          anonymous:
            username: anonymous
            roles: superuser
            authz_exception: false
      volumeClaimTemplates:
        - metadata:
            name: elasticsearch-data # Do not change this name unless you set up a volume mount for the data path.
          spec:
            accessModes:
              - ReadWriteOnce
            resources:
              requests:
                storage: 10Gi
            storageClassName: default
