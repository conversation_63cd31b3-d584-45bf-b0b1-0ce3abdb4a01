apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: cluster-setup
  namespace: argocd
spec:
  # Project description
  description: Cluster Wide Setup

  sourceRepos:
  #- 'https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/*'
  - 'https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/*'

  # Only permit applications to deploy to the guestbook namespace in the same cluster
  destinations:
  - namespace: '*'
    server: https://kubernetes.default.svc

  # Deny all cluster-scoped resources from being created, except for Namespace
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'

  # roles:
  # # A role which provides read-only access to all applications in the project
  # - name: read-only
  #   description: Read-only privileges to my-project
  #   policies:
  #   - p, proj:my-project:read-only, applications, get, my-project/*, allow
  #   groups:
  #   - my-oidc-group

  # # A role which provides sync privileges to only the guestbook-dev application, e.g. to provide
  # # sync privileges to a CI system
  # - name: ci-role
  #   description: Sync privileges for guestbook-dev
  #   policies:
  #   - p, proj:my-project:ci-role, applications, sync, my-project/guestbook-dev, allow

  #   # NOTE: JWT tokens can only be generated by the API server and the token is not persisted
  #   # anywhere by Argo CD. It can be prematurely revoked by removing the entry from this list.
  #   jwtTokens:
  #   - iat: 1535390316
---
apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: cluster-administration
  namespace: argocd
spec:
  # Project description
  description: Cluster Administration

  # Allow manifests to deploy from any Git repos
  sourceRepos:
  - https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git

  # Only permit applications to deploy to the guestbook namespace in the same cluster
  destinations:
  - namespace: '*'
    server: https://kubernetes.default.svc

  # Deny all cluster-scoped resources from being created, except for Namespace
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'

  # Allow all namespaced-scoped resources to be created, except for ResourceQuota, LimitRange, NetworkPolicy
#  namespaceResourceBlacklist:
#  - group: ''
#    kind: ResourceQuota

  # roles:
  # # A role which provides read-only access to all applications in the project
  # - name: read-only
  #   description: Read-only privileges to my-project
  #   policies:
  #   - p, proj:my-project:read-only, applications, get, my-project/*, allow
  #   groups:
  #   - my-oidc-group

  # # A role which provides sync privileges to only the guestbook-dev application, e.g. to provide
  # # sync privileges to a CI system
  # - name: ci-role
  #   description: Sync privileges for guestbook-dev
  #   policies:
  #   - p, proj:my-project:ci-role, applications, sync, my-project/guestbook-dev, allow

  #   # NOTE: JWT tokens can only be generated by the API server and the token is not persisted
  #   # anywhere by Argo CD. It can be prematurely revoked by removing the entry from this list.
  #   jwtTokens:
  #   - iat: 1535390316
---
apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: kingston
  namespace: argocd
spec:
  # Project description
  description: Cluster Apps DRE Cobra - Kingston

  # Allow manifests to deploy from any Git repos
  sourceRepos:
  - https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/helm-charts/*
  - https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
  - https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/dre-cobra/kingston.git

  # Only permit applications to deploy to the guestbook namespace in the same cluster
  destinations:
  - namespace: '*'
    server: https://kubernetes.default.svc

  # Deny all cluster-scoped resources from being created, except for Namespace
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'

  # Allow all namespaced-scoped resources to be created, except for ResourceQuota, LimitRange, NetworkPolicy
#  namespaceResourceBlacklist:
#  - group: ''
#    kind: ResourceQuota

  # roles:
  # # A role which provides read-only access to all applications in the project
  # - name: read-only
  #   description: Read-only privileges to my-project
  #   policies:
  #   - p, proj:my-project:read-only, applications, get, my-project/*, allow
  #   groups:
  #   - my-oidc-group

  # # A role which provides sync privileges to only the guestbook-dev application, e.g. to provide
  # # sync privileges to a CI system
  # - name: ci-role
  #   description: Sync privileges for guestbook-dev
  #   policies:
  #   - p, proj:my-project:ci-role, applications, sync, my-project/guestbook-dev, allow

  #   # NOTE: JWT tokens can only be generated by the API server and the token is not persisted
  #   # anywhere by Argo CD. It can be prematurely revoked by removing the entry from this list.
  #   jwtTokens:
  #   - iat: 1535390316
---
apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: internal
  namespace: argocd
spec:
  # Project description
  description: Cluster Apps DRE Cobra - Internal

  # Allow manifests to deploy from any Git repos
  sourceRepos:
  - https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/helm-charts/*
  - https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
  - https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/dre-cobra/internal.git

  # Only permit applications to deploy to the guestbook namespace in the same cluster
  destinations:
  - namespace: '*'
    server: https://kubernetes.default.svc

  # Deny all cluster-scoped resources from being created, except for Namespace
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'

  # Allow all namespaced-scoped resources to be created, except for ResourceQuota, LimitRange, NetworkPolicy
#  namespaceResourceBlacklist:
#  - group: ''
#    kind: ResourceQuota

  # roles:
  # # A role which provides read-only access to all applications in the project
  # - name: read-only
  #   description: Read-only privileges to my-project
  #   policies:
  #   - p, proj:my-project:read-only, applications, get, my-project/*, allow
  #   groups:
  #   - my-oidc-group

  # # A role which provides sync privileges to only the guestbook-dev application, e.g. to provide
  # # sync privileges to a CI system
  # - name: ci-role
  #   description: Sync privileges for guestbook-dev
  #   policies:
  #   - p, proj:my-project:ci-role, applications, sync, my-project/guestbook-dev, allow

  #   # NOTE: JWT tokens can only be generated by the API server and the token is not persisted
  #   # anywhere by Argo CD. It can be prematurely revoked by removing the entry from this list.
  #   jwtTokens:
  #   - iat: 1535390316
---
apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: observability
  namespace: argocd
spec:
  # Project description
  description: Cluster Apps DRE Cobra - Observability

  # Allow manifests to deploy from any Git repos
  sourceRepos:
  - https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/helm-charts/*
  - https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
  - https://artifactory.eu.ea.com/artifactory/dreeu-helm-virtual

  # Only permit applications to deploy to the guestbook namespace in the same cluster
  destinations:
  - namespace: '*'
    server: https://kubernetes.default.svc

  # Deny all cluster-scoped resources from being created, except for Namespace
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'

  # Allow all namespaced-scoped resources to be created, except for ResourceQuota, LimitRange, NetworkPolicy
#  namespaceResourceBlacklist:
#  - group: ''
#    kind: ResourceQuota

  # roles:
  # # A role which provides read-only access to all applications in the project
  # - name: read-only
  #   description: Read-only privileges to my-project
  #   policies:
  #   - p, proj:my-project:read-only, applications, get, my-project/*, allow
  #   groups:
  #   - my-oidc-group

  # # A role which provides sync privileges to only the guestbook-dev application, e.g. to provide
  # # sync privileges to a CI system
  # - name: ci-role
  #   description: Sync privileges for guestbook-dev
  #   policies:
  #   - p, proj:my-project:ci-role, applications, sync, my-project/guestbook-dev, allow

  #   # NOTE: JWT tokens can only be generated by the API server and the token is not persisted
  #   # anywhere by Argo CD. It can be prematurely revoked by removing the entry from this list.
  #   jwtTokens:
  #   - iat: 1535390316
---
apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: misc
  namespace: argocd
spec:
  # Project description
  description: Cluster Apps DRE Cobra - Misc

  # Allow manifests to deploy from any Git repos
  sourceRepos:
  - https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/helm-charts/*
  - https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
  - https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/dre-cobra/misc.git

  # Only permit applications to deploy to the guestbook namespace in the same cluster
  destinations:
  - namespace: '*'
    server: https://kubernetes.default.svc

  # Deny all cluster-scoped resources from being created, except for Namespace
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'

  # Allow all namespaced-scoped resources to be created, except for ResourceQuota, LimitRange, NetworkPolicy
#  namespaceResourceBlacklist:
#  - group: ''
#    kind: ResourceQuota

  # roles:
  # # A role which provides read-only access to all applications in the project
  # - name: read-only
  #   description: Read-only privileges to my-project
  #   policies:
  #   - p, proj:my-project:read-only, applications, get, my-project/*, allow
  #   groups:
  #   - my-oidc-group

  # # A role which provides sync privileges to only the guestbook-dev application, e.g. to provide
  # # sync privileges to a CI system
  # - name: ci-role
  #   description: Sync privileges for guestbook-dev
  #   policies:
  #   - p, proj:my-project:ci-role, applications, sync, my-project/guestbook-dev, allow

  #   # NOTE: JWT tokens can only be generated by the API server and the token is not persisted
  #   # anywhere by Argo CD. It can be prematurely revoked by removing the entry from this list.
  #   jwtTokens:
  #   - iat: 1535390316

---
apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: dice
  namespace: argocd
spec:
  # Project description
  description: Cluster Apps DRE Cobra - DICE

  # Allow manifests to deploy from any Git repos
  sourceRepos:
  - https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
  - https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/dre-cobra/dice.git

  # Only permit applications to deploy to the guestbook namespace in the same cluster
  destinations:
  - namespace: '*'
    server: https://kubernetes.default.svc

  # Deny all cluster-scoped resources from being created, except for Namespace
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'

  # Allow all namespaced-scoped resources to be created, except for ResourceQuota, LimitRange, NetworkPolicy
#  namespaceResourceBlacklist:
#  - group: ''
#    kind: ResourceQuota

  # roles:
  # # A role which provides read-only access to all applications in the project
  # - name: read-only
  #   description: Read-only privileges to my-project
  #   policies:
  #   - p, proj:my-project:read-only, applications, get, my-project/*, allow
  #   groups:
  #   - my-oidc-group

  # # A role which provides sync privileges to only the guestbook-dev application, e.g. to provide
  # # sync privileges to a CI system
  # - name: ci-role
  #   description: Sync privileges for guestbook-dev
  #   policies:
  #   - p, proj:my-project:ci-role, applications, sync, my-project/guestbook-dev, allow

  #   # NOTE: JWT tokens can only be generated by the API server and the token is not persisted
  #   # anywhere by Argo CD. It can be prematurely revoked by removing the entry from this list.
  #   jwtTokens:
  #   - iat: 1535390316

---
apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: granite
  namespace: argocd
spec:
  # Project description
  description: Cluster Apps DRE Cobra - Granite

  # Allow manifests to deploy from any Git repos
  sourceRepos:
  - https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/helm-charts/*
  - https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
  - https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/dre-cobra/granite.git

  # Only permit applications to deploy to the guestbook namespace in the same cluster
  destinations:
  - namespace: '*'
    server: https://kubernetes.default.svc

  # Deny all cluster-scoped resources from being created, except for Namespace
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'

  # Allow all namespaced-scoped resources to be created, except for ResourceQuota, LimitRange, NetworkPolicy
#  namespaceResourceBlacklist:
#  - group: ''
#    kind: ResourceQuota

  # roles:
  # # A role which provides read-only access to all applications in the project
  # - name: read-only
  #   description: Read-only privileges to my-project
  #   policies:
  #   - p, proj:my-project:read-only, applications, get, my-project/*, allow
  #   groups:
  #   - my-oidc-group

  # # A role which provides sync privileges to only the guestbook-dev application, e.g. to provide
  # # sync privileges to a CI system
  # - name: ci-role
  #   description: Sync privileges for guestbook-dev
  #   policies:
  #   - p, proj:my-project:ci-role, applications, sync, my-project/guestbook-dev, allow

---
apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: nfsupgrade
  namespace: argocd
spec:
  # Project description
  description: Cluster Apps DRE Cobra - NFSupgrade

  # Allow manifests to deploy from any Git repos
  sourceRepos:
  - 'https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/helm-charts/*'
  - https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git

  # Only permit applications to deploy to the guestbook namespace in the same cluster
  destinations:
  - namespace: '*'
    server: https://kubernetes.default.svc

  # Deny all cluster-scoped resources from being created, except for Namespace
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'

  # Allow all namespaced-scoped resources to be created, except for ResourceQuota, LimitRange, NetworkPolicy
#  namespaceResourceBlacklist:
#  - group: ''
#    kind: ResourceQuota

  # roles:
  # # A role which provides read-only access to all applications in the project
  # - name: read-only
  #   description: Read-only privileges to my-project
  #   policies:
  #   - p, proj:my-project:read-only, applications, get, my-project/*, allow
  #   groups:
  #   - my-oidc-group

  # # A role which provides sync privileges to only the guestbook-dev application, e.g. to provide
  # # sync privileges to a CI system
  # - name: ci-role
  #   description: Sync privileges for guestbook-dev
  #   policies:
  #   - p, proj:my-project:ci-role, applications, sync, my-project/guestbook-dev, allow

---
apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: bct
  namespace: argocd
spec:
  # Project description
  description: Cluster Apps DRE Cobra - BCT

  # Allow manifests to deploy from any Git repos
  sourceRepos:
  - 'https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/helm-charts/*'
  - https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git

  # Only permit applications to deploy to the guestbook namespace in the same cluster
  destinations:
  - namespace: '*'
    server: https://kubernetes.default.svc

  # Deny all cluster-scoped resources from being created, except for Namespace
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'

  # Allow all namespaced-scoped resources to be created, except for ResourceQuota, LimitRange, NetworkPolicy
#  namespaceResourceBlacklist:
#  - group: ''
#    kind: ResourceQuota

  # roles:
  # # A role which provides read-only access to all applications in the project
  # - name: read-only
  #   description: Read-only privileges to my-project
  #   policies:
  #   - p, proj:my-project:read-only, applications, get, my-project/*, allow
  #   groups:
  #   - my-oidc-group

  # # A role which provides sync privileges to only the guestbook-dev application, e.g. to provide
  # # sync privileges to a CI system
  # - name: ci-role
  #   description: Sync privileges for guestbook-dev
  #   policies:
  #   - p, proj:my-project:ci-role, applications, sync, my-project/guestbook-dev, allow

---
apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: fb1
  namespace: argocd
spec:
  # Project description
  description: Cluster Apps DRE Cobra - FB1

  # Allow manifests to deploy from any Git repos
  sourceRepos:
  - https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/helm-charts/*
  - https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git

  # Only permit applications to deploy to the guestbook namespace in the same cluster
  destinations:
  - namespace: '*'
    server: https://kubernetes.default.svc

  # Deny all cluster-scoped resources from being created, except for Namespace
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'

  # Allow all namespaced-scoped resources to be created, except for ResourceQuota, LimitRange, NetworkPolicy
#  namespaceResourceBlacklist:
#  - group: ''
#    kind: ResourceQuota

  # roles:
  # # A role which provides read-only access to all applications in the project
  # - name: read-only
  #   description: Read-only privileges to my-project
  #   policies:
  #   - p, proj:my-project:read-only, applications, get, my-project/*, allow
  #   groups:
  #   - my-oidc-group

  # # A role which provides sync privileges to only the guestbook-dev application, e.g. to provide
  # # sync privileges to a CI system
  # - name: ci-role
  #   description: Sync privileges for guestbook-dev
  #   policies:
  #   - p, proj:my-project:ci-role, applications, sync, my-project/guestbook-dev, allow

  #   # NOTE: JWT tokens can only be generated by the API server and the token is not persisted
  #   # anywhere by Argo CD. It can be prematurely revoked by removing the entry from this list.
  #   jwtTokens:
  #   - iat: 1535390316
