---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: bct-bilbo-kibana-ingress
  annotations:
    traefik.ingress.kubernetes.io/router.entrypoints: websecure
    traefik.ingress.kubernetes.io/router.middlewares: traefik-https-redirect@kubernetescrd
spec:
  tls:
    - hosts:
        - bct-bilbo-kibana.cobra.dre.ea.com
  rules:
    - host: bct-bilbo-kibana.cobra.dre.ea.com
      http:
        paths:
          - backend:
              service:
                name: bct-bilbo-kibana-kb-http
                port:
                  number: 5601
            path: /
            pathType: Prefix
