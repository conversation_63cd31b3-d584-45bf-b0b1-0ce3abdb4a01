---
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: dashing-vaultstore
spec:
  provider:
    vault:
      server: "https://ess.ea.com"
      path: "secrets/kv"
      namespace: "cds-dre-prod"
      auth:
        kubernetes:
          mountPath: "kubernetes/cobra/azure/dre-stockholm"
          role: cluster-apps-internal

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  labels:
    app.kubernetes.io/name: dashing-secrets
    app.kubernetes.io/part-of: dashing
  name: dashing-secrets
spec:
  secretStoreRef:
    name: dashing-vaultstore
    kind: SecretStore
  refreshInterval: "12h"
  target:
    template:
      metadata:
        annotations:
          argocd.argoproj.io/compare-options: IgnoreExtraneous
        labels:
          argocd.argoproj.io/secret-type: repo-creds
  data:
    # We have some other tokens in "monkey.commons" other than
    # Jenkins API that we didn't want to expose to dashing
    # container, That's why we're referencing secrets one by one here.
    - secretKey: JENKINS_API_KEY_DICE_BUILD_JENKINS
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: api_token_dice_build_jenkins
    - secretKey: JENKINS_API_KEY_FB1_JENKINS
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: api_token_fb1_jenkins
    - secretKey: JENKINS_API_KEY_KIN_RELEASE_JENKINS
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: api_token_dice_kin_release
    - secretKey: JENKINS_API_KEY_MER_DEV_JENKINS
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: api_token_mer_dev_jenkins
    - secretKey: JENKINS_API_KEY_DICE_JENKINS_TESTENV
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: api_token_dice_jenkins_testenv
    - secretKey: JENKINS_API_KEY_KIN_DEV_JENKINS
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: api_token_kin_dev_jenkins
    - secretKey: JENKINS_API_KEY_RES_GNT_DEV
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: api_token_res_gnt_dev
    - secretKey: JENKINS_API_KEY_BCT_AUTOTEST_JENKINS
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: api_token_bct_autotest_jenkins
    - secretKey: JENKINS_API_KEY_EXC_REL_JENKINS
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: api_token_exc_rel_jenkins
    - secretKey: JENKINS_API_KEY_KIN_AUTOTEST_JENKINS
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: api_token_kin_autotest_jenkins
    - secretKey: JENKINS_API_KEY_EXC_DEV_JENKINS
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: api_token_exc_dev_jenkins
    - secretKey: JENKINS_API_KEY_KIN_PREFLIGHT_JENKINS
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: api_token_kin_preflight_jenkins
    - secretKey: JENKINS_API_KEY_NFS_DEV_JENKINS
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: api_token_nfs_dev
    - secretKey: JENKINS_API_KEY_BCT_DEV_JENKINS
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: api_token_bct_dev_jenkins
    - secretKey: JENKINS_API_KEY_BCT_CH1_DEV_JENKINS
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: api_token_bct_ch1_dev_jenkins
    - secretKey: JENKINS_API_KEY_BCT_CH1_REL_JENKINS
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: api_token_bct_ch1_rel_jenkins
    - secretKey: JENKINS_API_KEY_BCT_CH1_AUTOTEST_JENKINS
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: api_token_bct_ch1_autotest_jenkins
    - secretKey: JENKINS_API_KEY_DICE_JOSS_JENKINS
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: api_token_dice_joss
    - secretKey: JENKINS_API_KEY_BCT_PREFLIGHT_JENKINS
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: api_token_bct_preflight_jenkins
    - secretKey: JENKINS_USERNAME
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: username
    - secretKey: ARTIFACTORY_USER
      remoteRef:
        key: artifacts/automation/dre-generic-federated/ro
        property: username
    - secretKey: ARTIFACTORY_TOKEN
      remoteRef:
        key: artifacts/automation/dre-generic-federated/ro
        property: reference_token