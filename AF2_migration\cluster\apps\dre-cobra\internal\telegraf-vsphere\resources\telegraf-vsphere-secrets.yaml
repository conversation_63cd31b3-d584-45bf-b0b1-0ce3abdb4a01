---
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: telegraf-vsphere-vaultstore
spec:
  provider:
    vault:
      server: "https://ess.ea.com"
      path: "secrets/kv"
      namespace: "cds-dre-prod"
      auth:
        kubernetes:
          mountPath: "kubernetes/cobra/azure/dre-stockholm"
          role: cluster-apps-internal

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  labels:
    app.kubernetes.io/name: telegraf-vsphere-secrets
    app.kubernetes.io/part-of: telegraf-vsphere
  name: telegraf-vsphere-secrets
spec:
  secretStoreRef:
    name: telegraf-vsphere-vaultstore
    kind: SecretStore
  refreshInterval: "12h"
  target:
    template:
      metadata:
        annotations:
          argocd.argoproj.io/compare-options: IgnoreExtraneous
        labels:
          argocd.argoproj.io/secret-type: repo-creds
  data:
    - secretKey: VSPHERE_USERNAME
      remoteRef:
        key: cobra/automation/vsphere/global
        property: EMAIL
    - secretKey: VSPHERE_PASSWORD
      remoteRef:
        key: cobra/automation/vsphere/global
        property: PASSWORD
