---
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: ssl-requester-vaultstore
  namespace: traefik
spec:
  provider:
    vault:
      server: "https://ess.ea.com"
      path: "secrets/kv"
      namespace: "cds-dre-prod"
      auth:
        kubernetes:
          mountPath: "kubernetes/cobra/azure/dre-stockholm"
          role: ssl-requester
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: default-tls
  namespace: traefik
spec:
  secretStoreRef:
    name: ssl-requester-vaultstore
    kind: SecretStore
  refreshInterval: "672h"
  target:
    template:
      metadata:
        annotations:
          argocd.argoproj.io/compare-options: IgnoreExtraneous
  data:
    - secretKey: tls.crt
      remoteRef:
        key: cobra/automation/kubernetes/azure/dre-stockholm/certificates/cobra.dre.ea.com
        property: chained
    - secretKey: tls.key
      remoteRef:
        key: cobra/automation/kubernetes/azure/dre-stockholm/certificates/cobra.dre.ea.com
        property: key
