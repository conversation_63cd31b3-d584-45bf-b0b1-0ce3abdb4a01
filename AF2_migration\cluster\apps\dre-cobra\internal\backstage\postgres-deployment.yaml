---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backstage-postgres
spec:
  replicas: 1
  selector:
    matchLabels:
      app: backstage-postgres
  template:
    metadata:
      labels:
        app: backstage-postgres
    spec:
      containers:
        - name: postgres
          image: docker.artifacts.ea.com/postgres:13.2-alpine
          imagePullPolicy: 'IfNotPresent'
          ports:
            - containerPort: 5432
          envFrom:
            - secretRef:
                name: backstage-secrets
          resources:
            requests:
              memory: "2Gi"
              cpu: "250m"
            limits:
              memory: "2Gi"
              cpu: "2"
          volumeMounts:
            - mountPath: /var/lib/postgresql/data
              name: backstage-postgresdb
              subPath: data
      volumes:
        - name: backstage-postgresdb
          persistentVolumeClaim:
            claimName: backstage-postgres-pvc
