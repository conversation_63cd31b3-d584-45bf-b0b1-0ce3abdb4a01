---
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: auto-taint-vaultstore
spec:
  provider:
    vault:
      server: "https://ess.ea.com"
      path: "secrets/kv"
      namespace: "cds-dre-prod"
      auth:
        kubernetes:
          mountPath: "kubernetes/cobra/azure/dre-stockholm"
          role: cluster-apps-internal

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  labels:
    app.kubernetes.io/name: auto-taint-secrets
    app.kubernetes.io/part-of: auto-taint
  name: auto-taint-secrets
spec:
  secretStoreRef:
    name: auto-taint-vaultstore
    kind: SecretStore
  refreshInterval: "12h"
  target:
    template:
      metadata:
        annotations:
          argocd.argoproj.io/compare-options: IgnoreExtraneous
        labels:
          argocd.argoproj.io/secret-type: repo-creds
  data:
    - secretKey: JENKINS_USER
      remoteRef:
        key: cobra/automation/accounts/svc_do_cobra02
        property: USERNAME
    - secretKey: JENKINS_KEY
      remoteRef:
        key: cobra/automation/accounts/svc_do_cobra02
        property: PASSWORD
    - secretKey: ARTIFACTORY_USERNAME
      remoteRef:
        key: cobra/automation/artifactory/docker
        property: ARTIFACTORY_USER
    - secretKey: ARTIFACTORY_PASSWORD
      remoteRef:
        key: cobra/automation/artifactory/docker
        property: ARTIFACTORY_TOKEN
    - secretKey: TF_HTTP_USERNAME
      remoteRef:
        key: cobra/automation/services/terraform-windows-vm
        property: TF_HTTP_USERNAME
    - secretKey: TF_HTTP_PASSWORD
      remoteRef:
        key: cobra/automation/services/terraform-windows-vm
        property: TF_HTTP_PASSWORD
  dataFrom:
    - extract:
        key: cobra/automation/services/auto-taint
    - extract:
        key: cobra/automation/slack/dre_cobra_slack_bot
