apiVersion: batch/v1
kind: CronJob
metadata:
  name: workload-restart-cronjob
  labels:
    app: workload-restart-cronjob
spec:
  schedule: "0 0 * * *"
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: workload-restart-cronjob
        spec:
          serviceAccountName: workload-restart-serviceaccount
          containers:
          - name: restart-daemonset
            image: docker.artifacts.at.ea.com/bitnami/kubectl:1.30.3
            command:
              - 'kubectl'
              - 'rollout'
              - 'restart'
              - 'daemonset/vector'
              - '-n'
              - 'observability'
          restartPolicy: Never
