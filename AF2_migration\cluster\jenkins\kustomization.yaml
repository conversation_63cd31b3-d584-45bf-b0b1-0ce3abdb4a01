apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: cobra

resources: 
  - https://gitlab+deploy-token-1218:<EMAIL>/DRE/kubernetes/base-apps/jenkins.git/?ref=main
  - ./resources/jenkins-ingress.yaml
  - ./resources/jenkins-secret.yaml




patches:
  - target:
      kind: StatefulSet
    patch: |-
      - op: add
        path: /spec/template/spec/imagePullSecrets
        value: [{ name: jenkins-secret }]
# This part need more to work out the vault credentuials and Jcasc configs. 
# using K8s we can use externalsecrets to deploy all secrets instead and jcasc config maps to deplloy jcasc 
# images:
#  - name: dre-docker-virtual.artifactory.ea.com/dre/jenkins/dre-jenkins-controller.alpine.jdk8
#    newName: dre-cobra-docker.artifactory.ea.com/dre-cobra-docker/jenkins
#    newTag: dun-autotest_2.303.2_latest


configMapGenerator:
  - name: jenkins-cm
    envs:
      - ./resources/jenkins-cm.env
    options:
      disableNameSuffixHash: true
  - name: vault-cm
    envs:
      - ./resources/vault-cm.env
    options:
      disableNameSuffixHash: true
  - name: gitmirror-cm
    files:
      - ./resources/mirror.list
    options:
      disableNameSuffixHash: true

