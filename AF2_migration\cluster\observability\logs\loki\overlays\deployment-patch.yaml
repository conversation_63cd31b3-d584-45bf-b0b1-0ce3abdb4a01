apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: loki
spec:
  template:
    spec:
      containers:
        - name: loki
          resources:
            requests:
              memory: "8Gi"
            limits:
              cpu: "3000m"
              memory: "8Gi"
  volumeClaimTemplates:
    - metadata:
        name: storage
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: 350Gi
