---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: loki-gateway-ingress
  annotations:
    traefik.ingress.kubernetes.io/router.entrypoints: web,websecure
    traefik.ingress.kubernetes.io/router.middlewares: traefik-https-redirect@kubernetescrd,application-observability-loki-basicauth@kubernetescrd
spec:
  tls:
    - hosts:
        - loki-dice.cobra.dre.ea.com
  rules:
    - host: loki-dice.cobra.dre.ea.com
      http:
        paths:
          - backend:
              service:
                name: loki-gateway
                port:
                  number: 80
            path: /
            pathType: Prefix