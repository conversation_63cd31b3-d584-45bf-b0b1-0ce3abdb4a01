apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: cs-security-af2auth
  # You'll usually want to add your resources to the argocd namespace.
  namespace: argocd
  # Add a this finalizer ONLY if you want these to cascade delete.
  # finalizers:
  #   - resources-finalizer.argocd.argoproj.io
spec:
  # The project the application belongs to.
  project: cluster-setup

  # Source of the application manifests
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
    targetRevision: master
    path: ./security/af2auth

  # Destination cluster and namespace to deploy the application
  destination:
    server: https://kubernetes.default.svc
    namespace: argocd

  syncPolicy:
    automated:
      prune: true
      selfHeal: true

  # ignoreDifferences:
