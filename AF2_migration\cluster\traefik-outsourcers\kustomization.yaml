# This is a secondary ingress controller for outsourcers access.
# any ingress with following annotation will be handled by this controller:
# annotaions:
#   kubernetes.io/ingress.class: traefik-outsourcers

apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: traefik-outsourcers
nameSuffix: -outsourcers

resources:
  - https://kustomize:<EMAIL>/DRE/kubernetes/cluster-base.git//traefik?ref=master
  - ./resources/ingress.yaml

patchesStrategicMerge:
  - ./overlays/clusterrolebinding.yaml
  - ./overlays/tlsstore.yaml
  - ./overlays/middleware.yaml
  - ./overlays/service.yaml
  - ./overlays/deployment.yaml

# update traefik config map with label selectors
configMapGenerator:
  - name: traefik-static
    behavior: replace
    files:
      - traefik.toml
