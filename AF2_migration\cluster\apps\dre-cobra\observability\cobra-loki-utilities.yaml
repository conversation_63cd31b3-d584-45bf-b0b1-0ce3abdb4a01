apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: cobra-loki-utilities
  namespace: argocd
  labels:
    project: observability
    group: loki
spec:
  project: observability
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
    path: ./apps/dre-cobra/observability/cobra-loki-utilities
    targetRevision: master
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: application-observability
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
    automated:
      prune: true
      selfHeal: true